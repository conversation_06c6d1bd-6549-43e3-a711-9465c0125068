{"name": "libnpmfund", "version": "7.0.3", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "Programmatic API for npm fund", "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmfund"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "git", "fund", "gitfund"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "test": "tap", "snap": "tap", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.8"}, "dependencies": {"@npmcli/arborist": "^9.1.0"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}