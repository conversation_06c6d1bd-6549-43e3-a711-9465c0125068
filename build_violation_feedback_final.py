#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞检违规反馈 - 最终构建脚本
专门为飞检违规反馈功能打包可执行文件
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def main():
    """主构建流程"""
    print("=" * 60)
    print("飞检违规反馈工具 - 最终构建脚本")
    print("=" * 60)
    print()
    
    project_root = Path(__file__).parent.absolute()
    
    try:
        # 1. 检查Python环境
        print("[1/6] 检查Python环境...")
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError("Python环境检查失败")
        print(f"Success: Python版本: {result.stdout.strip()}")
        
        # 2. 安装PyInstaller
        print("\n[2/6] 安装PyInstaller...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("警告: PyInstaller安装可能失败，尝试继续...")
        else:
            print("Success: PyInstaller安装完成")
        
        # 3. 安装飞检违规反馈功能必需的依赖
        print("\n[3/6] 安装项目依赖...")
        essential_deps = [
            'Flask==2.3.3',
            'pandas==2.1.1', 
            'openpyxl==3.1.2',
            'python-dotenv==1.0.0',
            'Werkzeug==2.3.7',
            'Jinja2==3.1.2'
        ]
        
        for dep in essential_deps:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Success: {dep} 安装成功")
            else:
                print(f"Error: {dep} 安装失败")
        
        # 尝试安装数据库依赖（可选）
        db_deps = ['psycopg2-binary', 'oracledb']
        print("尝试安装数据库依赖（可选）...")
        for dep in db_deps:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Success: {dep} 安装成功")
            else:
                print(f"Skip: {dep} 安装失败（跳过）")
        
        # 4. 生成构建配置
        print("\n[4/6] 生成构建配置...")
        result = subprocess.run([sys.executable, 'build_violation_feedback.py'], 
                              cwd=project_root, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"构建配置生成失败: {result.stderr}")
        print("Success: 构建配置生成完成")
        
        # 5. 执行打包
        print("\n[5/6] 开始打包飞检违规反馈应用程序...")
        print("这可能需要几分钟时间，请耐心等待...")
        
        spec_file = project_root / 'violation_feedback.spec'
        if not spec_file.exists():
            raise RuntimeError("spec文件不存在")
        
        start_time = time.time()
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller', 
            str(spec_file), '--clean', '--noconfirm'
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("打包失败，错误信息:")
            print(result.stderr)
            raise RuntimeError("PyInstaller打包失败")
        
        build_time = time.time() - start_time
        print(f"Success: 打包完成，耗时: {build_time:.1f}秒")
        
        # 6. 验证构建结果
        print("\n[6/6] 验证构建结果...")
        
        # 查找生成的可执行文件
        dist_dir = project_root / 'dist'
        exe_found = None
        
        for item in dist_dir.iterdir():
            if item.is_dir():
                exe_path = item / '飞检违规反馈.exe'
                if exe_path.exists():
                    exe_found = exe_path
                    break
        
        if not exe_found:
            raise RuntimeError("未找到生成的可执行文件")
        
        file_size = exe_found.stat().st_size
        print(f"Success: 找到可执行文件: {exe_found}")
        print(f"Success: 文件大小: {file_size / 1024 / 1024:.1f}MB")

        # 检查分发包内容
        dist_files = list(exe_found.parent.iterdir())
        print(f"Success: 分发包包含 {len(dist_files)} 个文件/目录")
        
        # 创建启动脚本和说明文件
        create_deployment_files(exe_found.parent)
        
        print("\n" + "=" * 60)
        print("Success: 飞检违规反馈工具构建成功完成！")
        print("=" * 60)
        print(f"输出位置: {exe_found.parent}")
        print(f"可执行文件: {exe_found.name}")
        print()
        print("使用说明:")
        print("1. 将整个文件夹复制到目标机器")
        print("2. 双击可执行文件启动应用")
        print("3. 在浏览器中访问 http://localhost:5000")
        print("4. 点击'飞检违规反馈'功能模块")
        print("5. 查看 README.txt 了解详细说明")
        print()
        
        # 询问是否测试
        try:
            test_choice = input("是否立即测试应用？(y/n): ").strip().lower()
            if test_choice == 'y':
                print("启动应用进行测试...")
                subprocess.Popen([str(exe_found)], cwd=str(exe_found.parent))
                print("应用已启动，请在浏览器中访问 http://localhost:5000")
                print("测试完成后请手动关闭应用")
        except KeyboardInterrupt:
            print("\n跳过测试")
        
        return True
        
    except Exception as e:
        print(f"\nError: 构建失败: {e}")
        return False

def create_deployment_files(dist_path):
    """创建部署文件"""
    # 创建启动脚本
    start_script = dist_path / 'start.bat'
    start_content = '''@echo off
chcp 65001 > nul
echo ========================================
echo 飞检违规反馈工具
echo ========================================
echo.
echo 正在启动应用...
echo 应用将在浏览器中打开: http://localhost:5000
echo.
echo 注意事项:
echo - 请勿关闭此命令行窗口
echo - 关闭此窗口将停止应用
echo - 如需停止应用，请按 Ctrl+C
echo.

timeout /t 2 /nobreak > nul
start "" "http://localhost:5000"

echo 启动中，请稍候...
echo.
"飞检违规反馈.exe"

echo.
echo 应用已停止
pause
'''
    
    with open(start_script, 'w', encoding='utf-8') as f:
        f.write(start_content)
    
    # 创建说明文件
    readme_file = dist_path / 'README.txt'
    readme_content = f'''飞检违规反馈工具 - 使用说明
========================================

快速开始:
1. 双击 "飞检违规反馈.exe" 启动应用
2. 或双击 "start.bat" 启动（推荐，会自动打开浏览器）
3. 在浏览器中访问 http://localhost:5000
4. 点击"飞检违规反馈"功能模块

功能说明:
- 随机病案抽取：从指定文件夹中随机抽取病案进行审查
- 违规问题记录：记录发现的违规问题
- 反馈报告生成：生成违规反馈报告
- Excel文件处理：支持批量处理Excel文件

使用步骤:
1. 准备源数据文件夹（包含Excel文件）
2. 设置输出文件夹路径
3. 配置抽取数量和其他参数
4. 执行随机抽取
5. 查看抽取结果和汇总报告

注意事项:
- 首次启动可能需要较长时间，请耐心等待
- 请确保5000端口未被其他程序占用
- 关闭命令行窗口将停止应用
- 建议使用Chrome、Edge或Firefox浏览器
- 源文件应包含"结算单据号"列

故障排除:
- 如果无法启动，请检查Windows防火墙设置
- 如果端口被占用，请关闭占用5000端口的其他程序
- 如遇到其他问题，请查看命令行窗口的错误信息

技术信息:
构建时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
Python版本: {sys.version.split()[0]}
打包工具: PyInstaller

版权信息:
本软件为飞检违规反馈工具，用于医疗文书随机抽样和违规问题反馈。
'''
    
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("Success: 部署文件已创建")

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1) 