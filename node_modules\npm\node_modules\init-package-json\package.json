{"name": "init-package-json", "version": "8.2.1", "main": "lib/init-package-json.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/init-package-json.git"}, "author": "GitHub Inc.", "license": "ISC", "description": "A node module to get your node module started", "dependencies": {"@npmcli/package-json": "^6.1.0", "npm-package-arg": "^12.0.0", "promzard": "^2.0.0", "read": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4", "validate-npm-package-name": "^6.0.0"}, "devDependencies": {"@npmcli/config": "^10.0.0", "@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "tap": "^16.0.1"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "tap": {"test-ignore": "fixtures/", "nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 300}, "keywords": ["init", "package.json", "package", "helper", "wizard", "wizerd", "prompt", "start"], "files": ["bin/", "lib/"], "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": true}}