
# g

Let's you easily get some module functions made into globals:

    require("g")("util");
	inspect(["foo"]);

or

    require("g")("log5");
	I("Hi mom");


## Yes, I know ...

Yes, I know.
It rubs you the wrong way. 
It goes against everything you've been taught.
It's sick and wrong. 
It's dangerous and irresponsible.
It's immoral and a crime against humanity.

So ...

THIS IS NOT FOR PRODUCTION SOFTWARE.
ONLY USE IT FOR PROTOTYPING AND PROOF-OF-CONCEPT WORK.
DO NOT USE THIS FOR ANYTHING TRULY IMPORTANT.

Okay?  We good?  Alright then.


