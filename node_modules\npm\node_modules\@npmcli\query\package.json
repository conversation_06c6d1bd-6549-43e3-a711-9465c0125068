{"name": "@npmcli/query", "version": "4.0.1", "description": "npm query parser and tools", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "keywords": ["ast", "npm", "npmcli", "parser", "postcss", "postcss-selector-parser", "query"], "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.2.0"}, "dependencies": {"postcss-selector-parser": "^7.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/npm/query.git"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}