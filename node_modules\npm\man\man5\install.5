.TH "INSTALL" "5" "May 2025" "NPM@11.4.0" ""
.SH "NAME"
\fBinstall\fR - Download and install node and npm
.SS "Description"
.P
To publish and install packages to and from the public npm registry, you must install Node.js and the npm command line interface using either a Node version manager or a Node installer. \fBWe strongly recommend using a Node version manager to install Node.js and npm.\fR We do not recommend using a Node installer, since the Node installation process installs npm in a directory with local permissions and can cause permissions errors when you run npm packages globally.
.SS "Overview"
.RS 0
.IP \(bu 4
\fBChecking your version of npm and Node.js\fR \fI(Checking your version of npm and Node.js)\fR
.IP \(bu 4
\fBUsing a Node version manager to install Node.js and npm\fR \fI(Using a Node version manager to install Node.js and npm)\fR
.IP \(bu 4
\fBUsing a Node installer to install Node.js and npm\fR \fI(Using a Node installer to install Node.js and npm)\fR
.RE 0

.SS "Checking your version of npm and Node.js"
.P
To see if you already have Node.js and npm installed and check the installed version, run the following commands:
.P
.RS 2
.nf
node -v
npm -v
.fi
.RE
.SS "Using a Node version manager to install Node.js and npm"
.P
Node version managers allow you to install and switch between multiple versions of Node.js and npm on your system so you can test your applications on multiple versions of npm to ensure they work for users on different versions. You can \fBsearch for them on GitHub\fR \fI\(lahttps://github.com/search?q=node+version+manager+archived%3Afalse&type=repositories&ref=advsearch\(ra\fR.
.SS "Using a Node installer to install Node.js and npm"
.P
If you are unable to use a Node version manager, you can use a Node installer to install both Node.js and npm on your system.
.RS 0
.IP \(bu 4
\fBNode.js installer\fR \fI\(lahttps://nodejs.org/en/download/\(ra\fR
.IP \(bu 4
\fBNodeSource installer\fR \fI\(lahttps://github.com/nodesource/distributions\(ra\fR. If you use Linux, we recommend that you use a NodeSource installer.
.RE 0

.SS "OS X or Windows Node installers"
.P
If you're using OS X or Windows, use one of the installers from the \fBNode.js download page\fR \fI\(lahttps://nodejs.org/en/download/\(ra\fR. Be sure to install the version labeled \fBLTS\fR. Other versions have not yet been tested with npm.
.SS "Linux or other operating systems Node installers"
.P
If you're using Linux or another operating system, use one of the following installers:
.RS 0
.IP \(bu 4
\fBNodeSource installer\fR \fI\(lahttps://github.com/nodesource/distributions\(ra\fR (recommended)
.IP \(bu 4
One of the installers on the \fBNode.js download page\fR \fI\(lahttps://nodejs.org/en/download/\(ra\fR
.RE 0

.P
Or see \fBthis page\fR \fI\(lahttps://nodejs.org/en/download/package-manager/\(ra\fR to install npm for Linux in the way many Linux developers prefer.
.SS "Less-common operating systems"
.P
For more information on installing Node.js on a variety of operating systems, see \fBthis page\fR \fI\(lahttps://nodejs.org/en/download/package-manager/\(ra\fR.
