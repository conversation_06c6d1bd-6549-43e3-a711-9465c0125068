{"name": "@npmcli/map-workspaces", "version": "4.0.2", "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "description": "Retrieves a name:pathname Map for a given workspaces config", "repository": {"type": "git", "url": "git+https://github.com/npm/map-workspaces.git"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "workspaces", "map-workspaces"], "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "npm run eslint", "pretest": "npm run lint", "test": "tap", "snap": "tap", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "tap": "^16.0.1"}, "dependencies": {"@npmcli/name-from-folder": "^3.0.0", "@npmcli/package-json": "^6.0.0", "glob": "^10.2.2", "minimatch": "^9.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": "true"}}