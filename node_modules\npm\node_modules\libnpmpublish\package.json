{"name": "libnpmpublish", "version": "11.0.0", "description": "Programmatic API for the bits behind npm publish and unpublish", "author": "GitHub Inc.", "main": "lib/index.js", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "files": ["bin/", "lib/"], "license": "ISC", "scripts": {"lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-globals": "^1.0.0", "@npmcli/mock-registry": "^1.0.0", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.8"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmpublish"}, "bugs": "https://github.com/npm/cli/issues", "homepage": "https://npmjs.com/package/libnpmpublish", "dependencies": {"ci-info": "^4.0.0", "normalize-package-data": "^7.0.0", "npm-package-arg": "^12.0.0", "npm-registry-fetch": "^18.0.1", "proc-log": "^5.0.0", "semver": "^7.3.7", "sigstore": "^3.0.0", "ssri": "^12.0.0"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}