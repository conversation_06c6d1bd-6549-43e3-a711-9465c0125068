# 正则表达式转义问题修复

**修复日期：** 2025-07-21  
**问题类型：** JavaScript正则表达式语法错误  
**修复状态：** 已完成  

## 问题描述

用户在使用重复规则审查功能时，浏览器控制台报错：

```
SyntaxError: Invalid regular expression: /(经电子内镜食管胃十二指肠黏膜剥离术（ESD))/gi: Unmatched ')'
```

## 问题分析

### 错误原因
在`highlightCommonNames`函数中，直接使用医保项目名称创建正则表达式，但没有转义特殊字符。当医保项目名称包含以下特殊字符时会导致正则表达式语法错误：

- 括号：`()` `（）`
- 方括号：`[]`
- 花括号：`{}`
- 星号：`*`
- 加号：`+`
- 问号：`?`
- 点号：`.`
- 管道符：`|`
- 反斜杠：`\`
- 脱字符：`^`
- 美元符：`$`

### 问题代码
```javascript
// 原始代码 - 有问题
commonNames.forEach(name => {
    const regex = new RegExp(`(${name})`, 'gi');  // 直接使用name，未转义
    highlightedText = highlightedText.replace(regex, '<mark class="bg-warning">$1</mark>');
});
```

### 具体案例
医保项目名称：`经电子内镜食管胃十二指肠黏膜剥离术（ESD）`

创建的正则表达式：`/(经电子内镜食管胃十二指肠黏膜剥离术（ESD）)/gi`

问题：括号`（ESD）`中的`）`没有匹配的开始括号，导致语法错误。

## 修复方案

### 1. 添加正则表达式转义
使用JavaScript标准的转义方法，将所有正则表达式特殊字符进行转义：

```javascript
const escapedName = name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
```

### 2. 添加异常处理
即使转义后仍可能出现问题，添加try-catch处理：

```javascript
try {
    const escapedName = name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedName})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark class="bg-warning">$1</mark>');
} catch (error) {
    // 降级处理：使用简单字符串替换
    console.warn('高亮显示失败，项目名称:', name, '错误:', error);
    const index = highlightedText.toLowerCase().indexOf(name.toLowerCase());
    if (index !== -1) {
        const originalText = highlightedText.substring(index, index + name.length);
        highlightedText = highlightedText.replace(originalText, `<mark class="bg-warning">${originalText}</mark>`);
    }
}
```

### 3. 修复后的完整代码

```javascript
function highlightCommonNames(text, commonNames) {
    if (!text || !commonNames || commonNames.length === 0) {
        return text || '';
    }

    let highlightedText = text;
    commonNames.forEach(name => {
        try {
            // 转义正则表达式特殊字符
            const escapedName = name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`(${escapedName})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark class="bg-warning">$1</mark>');
        } catch (error) {
            console.warn('高亮显示失败，项目名称:', name, '错误:', error);
            // 如果正则表达式仍然失败，使用简单的字符串替换
            const index = highlightedText.toLowerCase().indexOf(name.toLowerCase());
            if (index !== -1) {
                const originalText = highlightedText.substring(index, index + name.length);
                highlightedText = highlightedText.replace(originalText, `<mark class="bg-warning">${originalText}</mark>`);
            }
        }
    });
    return highlightedText;
}
```

## 修复效果

### 修复前
- 遇到特殊字符时抛出JavaScript语法错误
- 重复规则审查功能完全无法使用
- 浏览器控制台显示红色错误信息

### 修复后
- 正确处理包含特殊字符的医保项目名称
- 正常高亮显示匹配的项目名称
- 即使出现意外情况也有降级处理方案
- 重复规则审查功能正常工作

## 测试验证

### 1. 创建了测试页面
文件：`test_regex_escape.html`

测试用例包括：
- 正常文本
- 包含括号的项目名称：`经电子内镜食管胃十二指肠黏膜剥离术（ESD）`
- 包含特殊字符：`+*?`
- 包含方括号：`[血常规]`
- 包含花括号：`{方案A}`

### 2. 验证步骤
1. 打开`test_regex_escape.html`查看测试结果
2. 在医院规则管理页面测试重复规则审查功能
3. 检查浏览器控制台确认无错误信息

## 技术细节

### 正则表达式转义规则
```javascript
/[.*+?^${}()|[\]\\]/g
```

这个正则表达式匹配所有需要转义的特殊字符：
- `.` - 匹配任意字符
- `*` - 零次或多次
- `+` - 一次或多次  
- `?` - 零次或一次
- `^` - 行开始
- `$` - 行结束
- `{}` - 量词
- `()` - 分组
- `|` - 或操作
- `[]` - 字符类
- `\` - 转义字符

### 替换规则
```javascript
'\\$&'
```

- `$&` 表示匹配到的整个字符串
- `\\` 在字符串中表示一个反斜杠
- 结果是在每个特殊字符前添加反斜杠进行转义

## 预防措施

1. **输入验证**：在处理用户输入或数据库数据时，考虑特殊字符的影响
2. **异常处理**：对可能失败的操作添加try-catch处理
3. **降级方案**：提供备用的处理方法
4. **测试覆盖**：为包含特殊字符的场景编写测试用例

## 相关文件

- **修复文件**：`page/hospital_rules.html` (第1888-1912行)
- **测试文件**：`test_regex_escape.html`
- **文档文件**：`docs/regex_escape_fix.md`

## 总结

这个问题是典型的正则表达式转义问题，在处理用户数据或动态内容时很常见。通过添加适当的转义和异常处理，确保了功能的稳定性和用户体验。修复后，重复规则审查功能可以正常处理包含任何特殊字符的医保项目名称。
