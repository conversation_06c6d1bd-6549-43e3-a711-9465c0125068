{"name": "libnpmversion", "version": "8.0.1", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "library to do the things that 'npm version' does", "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmversion"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "npm run eslint", "test": "tap", "posttest": "npm run lint", "snap": "tap", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/template-oss": "4.23.6", "require-inject": "^1.4.4", "tap": "^16.3.8"}, "dependencies": {"@npmcli/git": "^6.0.1", "@npmcli/run-script": "^9.0.1", "json-parse-even-better-errors": "^4.0.0", "proc-log": "^5.0.0", "semver": "^7.3.7"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}}