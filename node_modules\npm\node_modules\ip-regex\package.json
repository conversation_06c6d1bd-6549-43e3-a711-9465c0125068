{"name": "ip-regex", "version": "5.0.0", "description": "Regular expression for matching IP addresses (IPv4 & IPv6)", "license": "MIT", "repository": "sindresorhus/ip-regex", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["ip", "ipv6", "ipv4", "regex", "regexp", "re", "match", "test", "find", "text", "pattern", "internet", "protocol", "address", "validate"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.19.1", "xo": "^0.47.0"}}