{"name": "cidr-regex", "version": "4.1.3", "description": "Regular expression for matching IP addresses in CIDR notation", "author": "silverwind <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (http://flipjs.io/)"], "repository": "silverwind/cidr-regex", "license": "BSD-2-<PERSON><PERSON>", "type": "module", "sideEffects": false, "main": "./dist/index.js", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "dependencies": {"ip-regex": "^5.0.0"}, "devDependencies": {"@types/node": "22.13.4", "eslint": "8.57.0", "eslint-config-silverwind": "99.0.0", "eslint-config-silverwind-typescript": "9.2.2", "typescript": "5.7.3", "typescript-config-silverwind": "8.0.0", "updates": "16.4.2", "versions": "12.1.3", "vite": "6.1.0", "vite-config-silverwind": "4.0.0", "vitest": "3.0.5", "vitest-config-silverwind": "10.0.0"}}