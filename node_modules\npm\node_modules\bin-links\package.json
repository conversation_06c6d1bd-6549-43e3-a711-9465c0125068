{"name": "bin-links", "version": "5.0.0", "description": "JavaScript package binary linker", "main": "./lib/index.js", "scripts": {"snap": "tap", "test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/bin-links.git"}, "keywords": ["npm", "link", "bins"], "license": "ISC", "dependencies": {"cmd-shim": "^7.0.0", "npm-normalize-package-bin": "^4.0.0", "proc-log": "^5.0.0", "read-cmd-shim": "^5.0.0", "write-file-atomic": "^6.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "require-inject": "^1.4.4", "tap": "^16.0.1"}, "tap": {"check-coverage": true, "coverage-map": "map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "4.23.3", "publish": true}}