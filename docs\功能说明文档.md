# 飞检数据处理工具箱 - 功能说明文档

本文档详细说明了“飞检数据处理工具箱”的各项功能、业务流程以及对应的后端实现方法，旨在帮助用户快速理解和使用本系统。

## 目录

<details>
<summary>点击展开/折叠</summary>

- [1. 飞检规则知识库](#1-飞检规则知识库)
- [2. 医院规则管理](#2-医院规则管理)
- [3. 飞检违规反馈](#3-飞检违规反馈)
- [4. 规则解析与入库](#4-规则解析与入库)
- [5. 智能审核](#5-智能审核)
- [6. SQL生成器](#6-sql生成器)
- [7. Excel文件拆分](#7-excel文件拆分)
- [8. Excel内容删除](#8-excel内容删除)
- [9. Excel比对工具](#9-excel比对工具)
- [10. Excel转SQL工具](#10-excel转sql工具)
- [11. 查找重复文件](#11-查找重复文件)
- [12. 数据校验](#12-数据校验)
- [13. 数据标准化](#13-数据标准化)

</details>

---

### 1. 飞检规则知识库

**功能描述:**

“飞检规则知识库”是系统的核心模块之一，用于集中管理和维护所有医保飞检规则。用户可以在此模块中查看、搜索、新增、修改和删除规则，并可以将规则与特定的城市和医保编码进行关联。该模块旨在为医保合规性审查提供一个全面、准确、易于维护的规则基础。

**业务流程:**

1.  **页面加载**: 
    - 用户点击左侧菜单栏的“飞检规则知识库”进入页面。
    - 前端页面 (`page/rule_knowledge_base.html`) 被加载。
    - 页面加载完成后，自动向后端发起请求获取所有规则列表。

2.  **获取规则列表**:
    - 前端通过 `GET /api/rules` 接口请求规则数据。
    - 后端 `get_rules()` 函数被调用。
    - 该函数会查询 `飞检规则知识库` 主表，并关联 `规则医保编码对照` 表来聚合每个规则关联的城市和规则来源。
    - 查询结果以 JSON 格式返回给前端，并在表格中展示。

3.  **新增/修改规则**:
    - 用户可以点击“新增规则”按钮或表格中的“编辑”按钮。
    - 弹出模态框，用户可以填写或修改规则的详细信息，包括规则名称、适用范围、行为认定、规则内涵、关联的医保编码等。
    - 点击“保存”后，前端将数据通过 `POST /api/rules/add` 或 `POST /api/rules/update` 接口发送到后端。
    - 后端对应的 `add_rule()` 或 `update_rule()` 函数会处理数据，并将其保存到数据库中。

4.  **删除规则**:
    - 用户在表格中选中一个或多个规则，点击“删除”按钮。
    - 前端将待删除规则的 ID 通过 `POST /api/rules/delete` 接口发送到后端。
    - 后端 `delete_rule()` 函数接收到 ID 列表后，在数据库中执行删除操作。

**相关方法:**

- `rule_knowledge_base()` (`app.py`): 渲染知识库主页面。
- `get_rules()` (`app.py`): (GET `/api/rules`) 获取所有规则列表。
- `add_rule()` (`app.py`): (POST `/api/rules/add`) 新增一条规则。
- `update_rule()` (`app.py`): (POST `/api/rules/update`) 更新一条规则。
- `delete_rule()` (`app.py`): (POST `/api/rules/delete`) 删除一条或多条规则。
- `get_rule_details()` (`app.py`): (GET `/api/rules/<int:rule_id>`) 获取单条规则的详细信息，用于编辑和查看。
- `get_cities_and_sources()` (`app.py`): (GET `/api/cities-and-sources`) 获取所有可选的城市和规则来源列表，用于前端下拉框。

### 2. 医院规则管理

**功能描述:**

“医院规则管理”模块是一个性化规则推荐系统，旨在帮助特定医院从主知识库中筛选和采纳适合自身的规则。用户可以管理医院列表，查看为每家医院推荐的规则，并决定是否采纳这些规则。此功能有助于实现规则的精细化和本地化管理。

**业务流程:**

1.  **管理医院信息**:
    - 用户在“医院规则管理”页面 (`page/hospital_rules.html`) 可以查看所有已录入的医院列表。
    - 通过 `GET /api/hospitals` 接口获取医院列表。
    - 用户可以新增医院信息（`POST /api/hospitals`）、修改现有医院信息（`PUT /api/hospitals/<int:hospital_id>`）或删除医院（`DELETE /api/hospitals/<int:hospital_id>`）。

2.  **查看推荐规则**:
    - 用户在列表中选择一家医院，然后点击“查看规则”或类似的按钮。
    - 前端调用 `GET /api/hospitals/<int:hospital_id>/recommended-rules` 接口。
    - 后端 `get_recommended_rules()` 函数会根据医院的收费项目，从 `飞检规则知识库` 中匹配并推荐相关规则。
    - 推荐逻辑基于医院收费项目名称与规则涉及的医保项目名称的相似度。

3.  **采纳规则**:
    - 对于推荐的规则列表，用户可以选择一个或多个规则进行“采纳”。
    - 点击“采纳”后，前端通过 `POST /api/hospitals/<int:hospital_id>/adopt-rules` 接口将选中的规则ID发送到后端。
    - 后端 `adopt_rules()` 函数会将这些规则与医院进行关联，通常是在一个独立的“已采纳规则”表中记录下来。

4.  **查看已采纳规则**:
    - 用户可以切换到“已采纳规则”视图。
    - 前端调用 `GET /api/hospitals/<int:hospital_id>/adopted-rules` 接口。
    - 后端 `get_adopted_rules()` 函数会查询并返回该医院所有已采纳的规则列表。

**相关方法:**

- `hospital_rules_page()` (`app.py`): 渲染医院规则管理页面。
- `get_hospitals()` (`app.py`): (GET `/api/hospitals`) 获取所有医院的列表。
- `create_hospital()` (`app.py`): (POST `/api/hospitals`) 创建一个新的医院条目。
- `update_hospital()` (`app.py`): (PUT `/api/hospitals/<int:hospital_id>`) 更新指定医院的信息。
- `delete_hospital()` (`app.py`): (DELETE `/api/hospitals/<int:hospital_id>`) 删除指定医院。
- `get_recommended_rules()` (`app.py`): (GET `/api/hospitals/<int:hospital_id>/recommended-rules`) 获取为指定医院推荐的规则。
- `adopt_rules()` (`app.py`): (POST `/api/hospitals/<int:hospital_id>/adopt-rules`) 为指定医院采纳规则。
- `get_adopted_rules()` (`app.py`): (GET `/api/hospitals/<int:hospital_id>/adopted-rules`) 获取指定医院已采纳的规则。

### 3. 飞检违规反馈

**功能描述:**

“飞检违规反馈”模块提供了一个对医疗文书（如病案首页）进行随机抽样并提交审查反馈的流程。用户可以从指定的文件夹中随机抽取一定数量的文件进行分析，然后对发现的违规问题进行记录和反馈。此功能旨在简化对大量病案的初步筛查工作。

**业务流程:**

1.  **进入功能页面**:
    - 用户通过主菜单访问“飞检违规反馈”功能，系统将渲染 `page/violation_feedback.html` 页面。

2.  **配置并执行随机抽样**:
    - 在页面上，用户需要提供以下信息：
        - **源文件夹路径**: 存放原始病案文件的文件夹。
        - **输出文件夹路径**: 用于保存抽样结果的文件夹。
        - **抽取数量**: 希望从源文件夹中随机抽取的样本文件数量。
        - **保留表头**: 是否在抽取的样本文件中保留原始文件的表头。
        - **移除重复项**: 是否在抽样过程中去除重复的记录。
    - 用户点击“开始抽取”或类似按钮，前端将这些参数通过 `POST` 请求发送到 `/random_sample` 或 `/random_sample_stream` 接口。

3.  **后端处理抽样**:
    - 后端 `random_sample()` 函数接收到请求后，会进行参数校验（如路径和数量的有效性）。
    - 它会实例化 `RandomSampler` 类，并调用其 `process_folder` 方法来执行实际的抽样逻辑。
    - 抽样完成后，会将结果（如成功信息、处理的文件数等）返回给前端。

4.  **提交违规反馈**:
    - (推测) 用户在审查完抽样文件后，可以在页面上填写违规详情、涉及的规则、以及其他备注信息。
    - 用户点击“提交反馈”，前端会将反馈内容发送到后端的特定API进行处理和保存（具体API待查）。

**相关方法:**

- `violation_feedback()` (`app.py`): (GET `/violation_feedback`) 渲染飞检违规反馈的主页面。
- `random_sample()` (`app.py`): (POST `/random_sample`) 接收抽样参数，执行一次性的随机病案抽取任务，并返回结果。
- `random_sample_stream()` (`app.py`): 功能与 `random_sample` 类似，但可能用于处理大规模数据并以流式方式返回进度或结果，以避免长时间等待和请求超时。

### 4. 规则解析与入库

**功能描述:**

“规则解析与入库”是一个核心功能，它允许用户上传包含医保飞检规则的SQL文件，系统会自动解析这些文件，提取出规则的元数据（如规则名称、城市、行为认定）、业务逻辑和关键条件，然后将这些结构化的规则信息存入“飞检规则知识库”中。该功能支持两种解析模式：一种是基于正则表达式的快速解析，另一种是基于`sqlglot`的深度解析，能够理解更复杂的SQL结构。

**业务流程:**

1.  **访问解析器页面**:
    - 用户通过主菜单进入“规则解析器”页面，系统渲染 `page/sql_rule_parser.html`。

2.  **上传SQL规则文件**:
    - 用户在此页面上选择一个或多个SQL文件进行上传。这些SQL文件通常在注释中包含了规则的元数据。
    - (推测) 页面上可能有一个“上传”按钮，对应后端的 `/upload_template` 或类似的通用文件上传接口，但其主要目的是将文件暂存到服务器，真正的解析由后续步骤触发。

3.  **触发解析操作**:
    - 用户在页面上选择已上传的文件，然后点击“解析”或“开始解析”按钮。
    - 前端将请求发送到后端的某个API（例如，一个通过WebSocket进行通信的异步任务接口）。

4.  **后端执行解析**:
    - 后端接收到请求后，会实例化 `SQLRuleParser` 或 `DeepSQLParser`。
    - 它会调用解析器的 `parse_file` 或 `parse_directory` 方法。
    - **解析过程**: 
        - **元数据解析**: `MetadataParser` 读取SQL文件头部的注释，提取规则名称、城市、行为认定等信息。
        - **规则分类**: `RuleClassifier` 根据SQL的关键字（如 `INTERSECT`, `SUM`）和结构特征，判断规则属于“重复收费”、“超量使用”等哪个类别。
        - **条件提取**: `ConditionExtractor` 提取出规则中的具体判断条件，如年龄范围、性别、医保项目编码等。
        - **深度解析 (可选)**: 如果选择深度解析模式，`DeepSQLParser` 会利用 `sqlglot` 将SQL文本转换成抽象语法树（AST），从而更精确地理解其逻辑结构。

5.  **结果展示与入库**:
    - 解析完成后，解析结果（`RuleInfo` 对象）会显示在前端页面上，供用户预览和确认。
    - 用户确认无误后，点击“确认入库”按钮。
    - 前端将解析好的结构化规则数据发送到 `/api/rules` (POST) 或类似的接口，后端 `add_rule()` 函数将其保存到 `飞检规则知识库` 数据库表中。

**相关方法:**

- `sql_rule_parser_page()` (`app.py`): (GET `/sql_rule_parser`) 渲染规则解析器的主页面。
- `upload_template()` (`app.py`): (POST `/upload_template`) 一个通用的文件上传接口，可能用于将SQL规则文件上传到服务器暂存。
- `SQLRuleParser.parse_file()` / `parse_directory()` (`sql_rule_parser/parser.py`): 核心解析函数，整合元数据解析、分类和条件提取。
- `DeepSQLParser.parse()` (`sql_deep_parser/parser.py`): 执行深度SQL解析的核心函数。
- `add_rule()` (`app.py`): (POST `/api/rules`) 将解析后的规则数据存入数据库（已在“飞检规则知识库”部分描述）。

### 5. 智能审核

**功能描述:**

提供一键式的飞检违规数据处理能力。用户只需提供一个包含多个Excel文件的文件夹路径，系统即可自动化地完成一系列复杂的审核与统计任务，最终生成统一的违规反馈报告。该功能旨在大幅提升数据审核效率，减少人工操作。

**核心特性:**

- **自动化流程**: 自动完成规则汇总、违规数据统计等多个步骤。
- **文件夹批量处理**: 无需手动上传单个文件，直接处理整个文件夹内的数据。
- **性能优化**: 支持多线程并发处理，大幅缩短大批量文件的处理时间。
- **实时进度反馈**: 通过界面实时展示处理进度和日志，用户可随时了解任务状态。

**业务流程:**

1. **访问页面**: 用户进入“飞检违规反馈”页面 (`/violation_feedback`)。
2. **输入路径**: 用户在页面上输入存放了待处理Excel文件的文件夹路径。
3. **配置选项**: 用户可以选择是否启用“性能优化”模式（多线程处理）。
4. **提交任务**: 用户点击“一键处理”按钮，前端将文件夹路径和配置信息通过POST请求发送至 `/api/violation_processing` 接口。
5. **后端处理**: 
    - 后端接收到请求后，首先验证文件夹路径的有效性。
    - 遍历文件夹，识别所有有效的Excel文件。
    - 根据用户是否选择性能优化，决定使用单线程或多线程并发处理文件。
    - 对每个文件执行规则汇总 (`/api/rule_summary`) 和违规金额统计 (`/api/static_money`) 等子任务。
    - 将所有处理结果进行整合。
6. **结果返回与下载**: 
    - 处理完成后，后端返回一个包含汇总数据和统计结果的JSON响应。
    - 前端解析响应，在界面上展示总违规数量、总金额等关键指标，并提供最终报告的下载链接。

**相关方法:**

- `violation_feedback()` (`app.py`): (GET `/violation_feedback`) - 渲染“飞检违规反馈”的前端页面。
- `api_violation_processing()` (`app.py`): (POST `/api/violation_processing`) - 核心处理接口，接收文件夹路径，协调整个自动化审核流程。
- `api_rule_summary()` (`app.py`): (POST `/api/rule_summary`) - (推测) 用于对单个或多个文件进行规则汇总的API。
- `api_static_money()` (`app.py`): (POST `/api/static_money`) - (推测) 用于统计违规金额的API。
- `test_folder()` (`app.py`): (POST `/api/test_folder`) - (推测) 用于测试用户提供的文件夹路径是否可访问及包含有效文件。

### 6. SQL生成器

**功能描述:**

“SQL生成器”是一个强大的工具，旨在帮助用户快速生成和执行SQL查询。它包含两个主要部分：一个通用的、基于模板的SQL生成器和一个专门针对“飞检规则”的SQL生成器。用户可以选择预定义的模板，填充变量，然后生成并执行SQL，从而简化数据查询和分析任务。

#### 6.1 通用SQL生成器

**业务流程:**

1.  **访问页面**: 用户通过菜单访问“SQL生成器”，系统渲染 `page/sql_generator.html` 页面。
2.  **选择模板**: 页面加载时，后端 `sql_generator()` 函数会根据 `template_type` 参数（默认为 `rule`）列出所有可用的SQL模板。
3.  **填充变量**: 用户选择一个模板后，系统会解析模板内容并提取出需要用户填写的变量（例如，在模板中使用 `{{variable_name}}` 格式定义的占位符）。
4.  **生成SQL**: 用户填写完所有变量后，系统会将这些值代入模板，生成最终的SQL查询语句。
5.  **执行SQL**: (推测) 用户可以复制生成的SQL，或通过页面上的按钮直接在选定的数据库连接上执行该SQL。

**相关方法:**

- `sql_generator()` (`app.py`): (GET `/sql_generator`) 渲染通用SQL生成器页面，并根据模板类型加载模板列表和变量。

#### 6.2 规则SQL生成器

**业务流程:**

1.  **访问页面**: 用户通过菜单访问“规则SQL生成器”，系统渲染 `page/rule_sql_generator.html` 页面。
2.  **加载规则信息**: 页面会调用后端API来获取用于生成SQL的必要信息，例如：
    - `GET /api/behavior_types`: 获取所有“行为认定”类型，用于下拉选择。
    - `GET /api/sql_generator/rules/<int:rule_id>`: 当用户选择一条具体规则时，调用此接口获取该规则的详细信息，如规则内涵、涉及的医保编码、违规天数/数量等。
3.  **生成SQL**: 根据用户选择的规则和行为类型，前端动态生成针对性的SQL查询。这通常涉及到将规则的参数（如医保编码、违规天数）嵌入到预设的SQL查询逻辑中。
4.  **执行与测试**: 用户可以对生成的SQL进行测试，连接到不同的数据库（如PostgreSQL）来验证其正确性和查询结果。

**相关方法:**

- `rule_sql_generator()` (`app.py`): (GET `/rule_sql_generator`) 渲染专门用于规则的SQL生成器页面。
- `get_behavior_types()`: (GET `/api/behavior_types`) 获取所有唯一的“行为认定”类型列表。
- `get_rule_for_sql_generator()`: (GET `/api/sql_generator/rules/<int:rule_id>`) 获取特定规则的详细信息，为生成SQL提供数据。

### 7. Excel文件拆分

**功能描述:**

“Excel文件拆分”工具允许用户上传一个Excel文件，并根据指定的列（分组列）将其拆分成多个独立的Excel文件。每个生成的文件都包含原始文件中具有相同分组列值的所有行。这个功能对于处理和分发按类别（如部门、地区、负责人等）组织的大型表格数据非常有用。

**业务流程:**

1.  **访问页面与上传文件**:
    - 用户访问“Excel文件拆分”页面（推测为 `excel_splitter.html`）。
    - 用户通过表单选择一个本地的Excel文件（`.xls` 或 `.xlsx`），并指定用于分组的列名。

2.  **提交拆分请求**:
    - 用户点击“上传并拆分”按钮，文件和分组列名将通过 `POST` 请求发送到 `/split_excel` 接口。

3.  **后端处理**:
    - `split_excel()` 函数首先验证文件是否已上传且文件类型是否允许。
    - 上传的文件被保存到服务器的 `UPLOAD_FOLDER` 目录中。
    - 系统根据当前时间戳在 `OUTPUT_FOLDER` 中创建一个唯一的子文件夹，用于存放拆分后的文件。
    - 调用 `excel_splitter.py` 模块中的核心 `split_excel` 函数，传入上传文件的路径、输出目录和分组列名。
    - 核心函数读取Excel文件，根据指定列的值对数据进行分组，并将每个组的数据写入一个新的Excel文件中。

4.  **打包并下载结果**:
    - 拆分成功后，后端会将所有生成的小文件打包成一个ZIP压缩文件。
    - 这个ZIP文件在内存中创建，不会在服务器上留下临时压缩文件。
    - 最后，通过 `send_file` 将ZIP文件作为附件发送给用户，用户浏览器会自动触发下载。
    - 前端页面会显示成功消息，告知用户拆分了多少个文件以及文件的保存路径。

**相关方法:**

- `split_excel()` (`app.py`): (POST `/split_excel`) 这是处理文件上传、拆分和打包下载的核心路由函数。它协调了文件的保存、调用拆分逻辑、以及最终结果的返回。
- `excel_splitter.split_excel()`: (位于 `excel_splitter.py` 中) 这是实际执行Excel文件读取、按列分组和写入新文件的核心逻辑函数（非路由）。

### 8. Excel内容删除

**功能描述:**

“Excel内容删除”工具允许用户上传一个Excel文件，并根据指定的条件（例如，包含特定关键字的行）来删除文件中的行。这对于数据清洗和预处理非常有用，可以帮助用户快速移除不需要的数据。

**业务流程:**

1.  **访问页面与上传文件**:
    - 用户访问“Excel内容删除”页面（推测为 `excel_content_deleter.html`）。
    - 用户通过表单选择一个本地的Excel文件，并输入要删除的行所包含的关键字。

2.  **提交删除请求**:
    - 用户点击“上传并处理”按钮，文件和关键字将通过 `POST` 请求发送到 `/delete_content` 接口。

3.  **后端处理**:
    - `delete_content()` 函数首先验证文件是否已上传且文件类型是否允许。
    - 上传的文件被保存到服务器的 `UPLOAD_FOLDER` 目录中。
    - 调用 `excel_content_deleter.py` 模块中的核心 `delete_rows_by_keyword` 函数，传入上传文件的路径和关键字。
    - 核心函数读取Excel文件，遍历所有行，如果某一行包含指定的关键字，则将其删除。
    - 处理后的数据被保存到一个新的Excel文件中，通常位于 `OUTPUT_FOLDER`。

4.  **下载结果**:
    - 处理成功后，后端将生成的新Excel文件作为附件发送给用户，用户浏览器会自动触发下载。

**相关方法:**

- `delete_content()` (`app.py`): (POST `/delete_content`) 这是处理文件上传、内容删除和结果下载的核心路由函数。
- `excel_content_deleter.delete_rows_by_keyword()`: (位于 `excel_content_deleter.py` 中) 这是实际执行Excel文件读取、按关键字删除行和写入新文件的核心逻辑函数（非路由）。

### 9. Excel比对工具

**功能描述:**

“Excel比对工具”允许用户上传两个Excel文件，并对它们进行逐行比对，找出两个文件之间的差异。比对结果会生成一个包含差异详情的新Excel文件，清晰地标出新增、删除或修改的行。这对于版本控制、数据核对和审计追踪非常有用。

**业务流程:**

1.  **访问页面与上传文件**:
    - 用户访问“Excel比对工具”页面（推测为 `excel_comparator.html`）。
    - 用户通过表单分别上传两个需要比对的Excel文件（文件A和文件B）。

2.  **提交比对请求**:
    - 用户点击“开始比对”按钮，两个文件将通过 `POST` 请求发送到 `/compare_excels` 接口。

3.  **后端处理**:
    - `compare_excels()` 函数首先验证两个文件是否都已上传且文件类型是否允许。
    - 上传的文件被保存到服务器的 `UPLOAD_FOLDER` 目录中。
    - 调用 `excel_comparator.py` 模块中的核心 `compare_files` 函数，传入两个文件的路径。
    - 核心函数逐行读取两个文件，并进行内容比对，记录下所有差异。
    - 比对结果被写入一个新的Excel文件中，通常保存在 `OUTPUT_FOLDER`，其中差异行会被特殊标记（例如，使用不同的背景色）。

4.  **下载比对报告**:
    - 比对完成后，后端将生成的差异报告Excel文件作为附件发送给用户，用户浏览器会自动触发下载。

**相关方法:**

- `compare_excels()` (`app.py`): (POST `/compare_excels`) 这是处理文件上传、执行比对和下载比对报告的核心路由函数。
- `excel_comparator.compare_files()`: (位于 `excel_comparator.py` 中) 这是实际执行两个Excel文件内容比对并生成差异报告的核心逻辑函数（非路由）。

### 10. Excel转SQL工具

**功能描述:**

“Excel转SQL工具”允许用户上传一个Excel文件，并将其内容转换成SQL `INSERT` 语句。用户可以指定目标数据库表名，工具会自动生成将Excel中每一行数据插入到该表的SQL脚本。这对于从表格数据快速迁移到关系型数据库非常方便。

**业务流程:**

1.  **访问页面与上传文件**:
    - 用户访问“Excel转SQL工具”页面（推测为 `excel_to_sql.html`）。
    - 用户通过表单选择一个本地的Excel文件，并输入目标数据库的表名。

2.  **提交转换请求**:
    - 用户点击“生成SQL”按钮，文件和表名将通过 `POST` 请求发送到 `/excel_to_sql` 接口。

3.  **后端处理**:
    - `excel_to_sql()` 函数首先验证文件是否已上传且文件类型是否允许。
    - 上传的文件被保存到服务器的 `UPLOAD_FOLDER` 目录中。
    - 调用 `excel_to_sql.py` 模块中的核心 `generate_sql_from_excel` 函数，传入文件路径和表名。
    - 核心函数读取Excel文件的内容（通常假设第一行为列名），然后为文件中的每一行数据生成一条对应的SQL `INSERT` 语句。
    - 所有生成的SQL语句被合并成一个单独的 `.sql` 文件，保存在 `OUTPUT_FOLDER`。

4.  **下载SQL文件**:
    - 转换完成后，后端将生成的 `.sql` 文件作为附件发送给用户，用户浏览器会自动触发下载。

**相关方法:**

- `excel_to_sql()` (`app.py`): (POST `/excel_to_sql`) 这是处理文件上传、执行转换和下载SQL文件的核心路由函数。
- `excel_to_sql.generate_sql_from_excel()`: (位于 `excel_to_sql.py` 中) 这是实际执行Excel文件读取并生成SQL `INSERT` 语句的核心逻辑函数（非路由）。

### 11. 查找重复文件

**功能描述:**

“查找重复文件”工具可以扫描用户指定的文件夹，并找出其中内容完全相同的文件。它通过计算每个文件的哈希值（如MD5或SHA256）来进行精确比对，而不是仅仅比较文件名。这对于清理磁盘空间、整理文件和数据去重非常有用。

**业务流程:**

1.  **访问页面与指定目录**:
    - 用户访问“查找重复文件”页面（推测为 `duplicate_finder.html`）。
    - 用户在页面上输入或选择一个要扫描的文件夹路径。

2.  **提交查找请求**:
    - 用户点击“开始查找”按钮，文件夹路径将通过 `POST` 请求发送到 `/find_duplicates` 接口。

3.  **后端处理**:
    - `find_duplicates()` 函数首先验证指定的路径是否存在且是一个目录。
    - 调用 `duplicate_finder.py` 模块中的核心 `find_duplicate_files` 函数，传入要扫描的目录路径。
    - 核心函数会递归地遍历指定目录下的所有文件，计算每个文件的哈希值。
    - 它将文件的哈希值作为键，将文件路径列表作为值，存储在一个字典中。如果多个文件具有相同的哈希值，它们就会被归为重复文件。

4.  **展示结果**:
    - 查找完成后，后端会将包含重复文件信息的字典返回给前端。
    - 前端页面会将结果以清晰的格式（例如，按组显示重复文件列表）展示给用户。
    - 用户可以根据列表手动删除或管理这些重复文件。

**相关方法:**

- `find_duplicates()` (`app.py`): (POST `/find_duplicates`) 这是接收文件夹路径、执行重复文件查找并返回结果的核心路由函数。
- `duplicate_finder.find_duplicate_files()`: (位于 `duplicate_finder.py` 中) 这是实际执行文件遍历、哈希计算和重复项识别的核心逻辑函数（非路由）。

### 12. 数据校验

**功能描述:**

“数据校验”功能提供了一套可配置的规则，用于验证上传的数据文件（如Excel或CSV）是否符合预定义的格式和约束。例如，它可以检查某个字段是否为空、数据类型是否正确、值是否在有效范围内等。这对于保证数据质量、防止脏数据进入系统至关重要。

**业务流程:**

1.  **定义校验规则**:
    - (推测) 系统管理员或高级用户可以在一个专门的界面（或通过配置文件）定义数据校验规则。这些规则可能包括字段名、数据类型、是否允许为空、正则表达式模式等。

2.  **访问页面与上传文件**:
    - 用户访问“数据校验”页面（推测为 `data_validator.html`）。
    - 用户上传需要校验的数据文件，并选择要应用的校验规则集。

3.  **提交校验请求**:
    - 用户点击“开始校验”按钮，文件和所选规则集将通过 `POST` 请求发送到 `/validate_data` 接口。

4.  **后端处理**:
    - `validate_data()` 函数接收文件和规则信息。
    - 调用 `data_validator.py` 模块中的核心 `validate` 函数。
    - 核心函数根据规则，逐行逐字段地检查数据。它会记录所有不符合规则的错误，包括行号、字段名和具体的错误信息。

5.  **生成并下载校验报告**:
    - 校验完成后，系统会生成一份详细的校验报告，通常是一个Excel文件，其中列出了所有错误及其位置。
    - 后端将这份报告作为附件发送给用户下载。

**相关方法:**

- `validate_data()` (`app.py`): (POST `/validate_data`) 这是处理文件上传、执行数据校验和返回校验报告的核心路由函数。
- `data_validator.validate()`: (位于 `data_validator.py` 中) 这是根据预定义规则对数据文件进行校验的核心逻辑函数（非路由）。

### 13. 数据标准化

**功能描述:**

“数据标准化”功能用于将不同来源、不同格式的数据转换成统一、标准的格式。例如，它可以将日期统一为 `YYYY-MM-DD` 格式，将地区名称转换为标准代码，或者对数值进行归一化处理。这对于数据整合、分析和后续处理至关重要。

**业务流程:**

1.  **定义标准化映射**:
    - (推测) 系统管理员或高级用户可以在一个专门的界面（或通过配置文件）定义标准化规则和映射表。例如，定义一个“城市名称”到“城市编码”的映射。

2.  **访问页面与上传文件**:
    - 用户访问“数据标准化”页面（推测为 `data_normalizer.html`）。
    - 用户上传需要标准化的数据文件，并选择要应用的标准化规则集。

3.  **提交标准化请求**:
    - 用户点击“开始标准化”按钮，文件和所选规则集将通过 `POST` 请求发送到 `/normalize_data` 接口。

4.  **后端处理**:
    - `normalize_data()` 函数接收文件和规则信息。
    - 调用 `data_normalizer.py` 模块中的核心 `normalize` 函数。
    - 核心函数根据规则，对指定字段的值进行查找和替换，或者应用格式化函数。

5.  **生成并下载标准化文件**:
    - 标准化完成后，系统会生成一个新的数据文件，其中包含了格式统一的数据。
    - 后端将这个新文件作为附件发送给用户下载。

**相关方法:**

- `normalize_data()` (`app.py`): (POST `/normalize_data`) 这是处理文件上传、执行数据标准化和返回结果文件的核心路由函数。
- `data_normalizer.normalize()` (`data_normalizer.py`): 这是根据预定义规则对数据文件进行标准化处理的核心逻辑函数（非路由）。