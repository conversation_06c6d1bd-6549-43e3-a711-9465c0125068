.TH "PACKAGE.JSON" "5" "May 2025" "NPM@11.4.0" ""
.SH "NAME"
\fBpackage.json\fR - Specifics of npm's package.json handling
.SS "Description"
.P
This document is all you need to know about what's required in your package.json file. It must be actual JSON, not just a JavaScript object literal.
.P
A lot of the behavior described in this document is affected by the config settings described in npm help config.
.SS "name"
.P
If you plan to publish your package, the \fImost\fR important things in your package.json are the name and version fields as they will be required. The name and version together form an identifier that is assumed to be completely unique. Changes to the package should come along with changes to the version. If you don't plan to publish your package, the name and version fields are optional.
.P
The name is what your thing is called.
.P
Some rules:
.RS 0
.IP \(bu 4
The name must be less than or equal to 214 characters. This includes the scope for scoped packages.
.IP \(bu 4
The names of scoped packages can begin with a dot or an underscore. This is not permitted without a scope.
.IP \(bu 4
New packages must not have uppercase letters in the name.
.IP \(bu 4
The name ends up being part of a URL, an argument on the command line, and a folder name. Therefore, the name can't contain any non-URL-safe characters.
.RE 0

.P
Some tips:
.RS 0
.IP \(bu 4
Don't use the same name as a core Node module.
.IP \(bu 4
Don't put "js" or "node" in the name. It's assumed that it's js, since you're writing a package.json file, and you can specify the engine using the "\fBengines\fR \fI(engines)\fR" field. (See below.)
.IP \(bu 4
The name will probably be passed as an argument to require(), so it should be something short, but also reasonably descriptive.
.IP \(bu 4
You may want to check the npm registry to see if there's something by that name already, before you get too attached to it. \fI\(lahttps://www.npmjs.com/\(ra\fR
.RE 0

.P
A name can be optionally prefixed by a scope, e.g. \fB@npm/example\fR. See npm help scope for more detail.
.SS "version"
.P
If you plan to publish your package, the \fImost\fR important things in your package.json are the name and version fields as they will be required. The name and version together form an identifier that is assumed to be completely unique. Changes to the package should come along with changes to the version. If you don't plan to publish your package, the name and version fields are optional.
.P
Version must be parseable by \fBnode-semver\fR \fI\(lahttps://github.com/npm/node-semver\(ra\fR, which is bundled with npm as a dependency. (\fBnpm install semver\fR to use it yourself.)
.SS "description"
.P
Put a description in it. It's a string. This helps people discover your package, as it's listed in \fBnpm search\fR.
.SS "keywords"
.P
Put keywords in it. It's an array of strings. This helps people discover your package as it's listed in \fBnpm search\fR.
.SS "homepage"
.P
The URL to the project homepage.
.P
Example:
.P
.RS 2
.nf
"homepage": "https://github.com/npm/example#readme"
.fi
.RE
.SS "bugs"
.P
The URL to your project's issue tracker and / or the email address to which issues should be reported. These are helpful for people who encounter issues with your package.
.P
It should look like this:
.P
.RS 2
.nf
{
  "bugs": {
    "url": "https://github.com/npm/example/issues",
    "email": "<EMAIL>"
  }
}
.fi
.RE
.P
You can specify either one or both values. If you want to provide only a URL, you can specify the value for "bugs" as a simple string instead of an object.
.P
If a URL is provided, it will be used by the \fBnpm bugs\fR command.
.SS "license"
.P
You should specify a license for your package so that people know how they are permitted to use it, and any restrictions you're placing on it.
.P
If you're using a common license such as BSD-2-Clause or MIT, add a current SPDX license identifier for the license you're using, like this:
.P
.RS 2
.nf
{
  "license" : "BSD-3-Clause"
}
.fi
.RE
.P
You can check \fBthe full list of SPDX license IDs\fR \fI\(lahttps://spdx.org/licenses/\(ra\fR. Ideally, you should pick one that is \fBOSI\fR \fI\(lahttps://opensource.org/licenses/\(ra\fR approved.
.P
If your package is licensed under multiple common licenses, use an \fBSPDX license expression syntax version 2.0 string\fR \fI\(lahttps://spdx.dev/specifications/\(ra\fR, like this:
.P
.RS 2
.nf
{
  "license" : "(ISC OR GPL-3.0)"
}
.fi
.RE
.P
If you are using a license that hasn't been assigned an SPDX identifier, or if you are using a custom license, use a string value like this one:
.P
.RS 2
.nf
{
  "license" : "SEE LICENSE IN <filename>"
}
.fi
.RE
.P
Then include a file named \fB<filename>\fR at the top level of the package.
.P
Some old packages used license objects or a "licenses" property containing an array of license objects:
.P
.RS 2
.nf
// Not valid metadata
{
  "license" : {
    "type" : "ISC",
    "url" : "https://opensource.org/licenses/ISC"
  }
}

// Not valid metadata
{
  "licenses" : \[lB]
    {
      "type": "MIT",
      "url": "https://www.opensource.org/licenses/mit-license.php"
    },
    {
      "type": "Apache-2.0",
      "url": "https://opensource.org/licenses/apache2.0.php"
    }
  \[rB]
}
.fi
.RE
.P
Those styles are now deprecated. Instead, use SPDX expressions, like this:
.P
.RS 2
.nf
{
  "license": "ISC"
}
.fi
.RE
.P
.RS 2
.nf
{
  "license": "(MIT OR Apache-2.0)"
}
.fi
.RE
.P
Finally, if you do not wish to grant others the right to use a private or unpublished package under any terms:
.P
.RS 2
.nf
{
  "license": "UNLICENSED"
}
.fi
.RE
.P
Consider also setting \fB"private": true\fR to prevent accidental publication.
.SS "people fields: author, contributors"
.P
The "author" is one person. "contributors" is an array of people. A "person" is an object with a "name" field and optionally "url" and "email", like this:
.P
.RS 2
.nf
{
  "name" : "Barney Rubble",
  "email" : "<EMAIL>",
  "url" : "http://barnyrubble.npmjs.com/"
}
.fi
.RE
.P
Or you can shorten that all into a single string, and npm will parse it for you:
.P
.RS 2
.nf
{
  "author": "Barney Rubble <<EMAIL>> (http://barnyrubble.npmjs.com/)"
}
.fi
.RE
.P
Both email and url are optional either way.
.P
npm also sets a top-level "maintainers" field with your npm user info.
.SS "funding"
.P
You can specify an object containing a URL that provides up-to-date information about ways to help fund development of your package, a string URL, or an array of objects and string URLs:
.P
.RS 2
.nf
{
  "funding": {
    "type" : "individual",
    "url" : "http://npmjs.com/donate"
  }
}
.fi
.RE
.P
.RS 2
.nf
{
  "funding": {
    "type" : "patreon",
    "url" : "https://www.patreon.com/user"
  }
}
.fi
.RE
.P
.RS 2
.nf
{
  "funding": "http://npmjs.com/donate"
}
.fi
.RE
.P
.RS 2
.nf
{
  "funding": \[lB]
    {
      "type" : "individual",
      "url" : "http://npmjs.com/donate"
    },
    "http://npmjs.com/donate-also",
    {
      "type" : "patreon",
      "url" : "https://www.patreon.com/user"
    }
  \[rB]
}
.fi
.RE
.P
Users can use the \fBnpm fund\fR subcommand to list the \fBfunding\fR URLs of all dependencies of their project, direct and indirect. A shortcut to visit each funding URL is also available when providing the project name such as: \fBnpm fund <projectname>\fR (when there are multiple URLs, the first one will be visited)
.SS "files"
.P
The optional \fBfiles\fR field is an array of file patterns that describes the entries to be included when your package is installed as a dependency. File patterns follow a similar syntax to \fB.gitignore\fR, but reversed: including a file, directory, or glob pattern (\fB*\fR, \fB**/*\fR, and such) will make it so that file is included in the tarball when it's packed. Omitting the field will make it default to \fB\[lB]"*"\[rB]\fR, which means it will include all files.
.P
Some special files and directories are also included or excluded regardless of whether they exist in the \fBfiles\fR array (see below).
.P
You can also provide a \fB.npmignore\fR file in the root of your package or in subdirectories, which will keep files from being included. At the root of your package it will not override the "files" field, but in subdirectories it will. The \fB.npmignore\fR file works just like a \fB.gitignore\fR. If there is a \fB.gitignore\fR file, and \fB.npmignore\fR is missing, \fB.gitignore\fR's contents will be used instead.
.P
Certain files are always included, regardless of settings:
.RS 0
.IP \(bu 4
\fBpackage.json\fR
.IP \(bu 4
\fBREADME\fR
.IP \(bu 4
\fBLICENSE\fR / \fBLICENCE\fR
.IP \(bu 4
The file in the "main" field
.IP \(bu 4
The file(s) in the "bin" field
.RE 0

.P
\fBREADME\fR & \fBLICENSE\fR can have any case and extension.
.P
Some files are always ignored by default:
.RS 0
.IP \(bu 4
\fB*.orig\fR
.IP \(bu 4
\fB.*.swp\fR
.IP \(bu 4
\fB.DS_Store\fR
.IP \(bu 4
\fB._*\fR
.IP \(bu 4
\fB.git\fR
.IP \(bu 4
\fB.hg\fR
.IP \(bu 4
\fB.lock-wscript\fR
.IP \(bu 4
\fB.npmrc\fR
.IP \(bu 4
\fB.svn\fR
.IP \(bu 4
\fB.wafpickle-N\fR
.IP \(bu 4
\fBCVS\fR
.IP \(bu 4
\fBconfig.gypi\fR
.IP \(bu 4
\fBnode_modules\fR
.IP \(bu 4
\fBnpm-debug.log\fR
.IP \(bu 4
\fBpackage-lock.json\fR (use \fB\fBnpm-shrinkwrap.json\fR\fR \fI\(la/configuring-npm/npm-shrinkwrap-json\(ra\fR if you wish it to be published)
.IP \(bu 4
\fBpnpm-lock.yaml\fR
.IP \(bu 4
\fByarn.lock\fR
.IP \(bu 4
\fBbun.lockb\fR
.RE 0

.P
Most of these ignored files can be included specifically if included in the \fBfiles\fR globs. Exceptions to this are:
.RS 0
.IP \(bu 4
\fB.git\fR
.IP \(bu 4
\fB.npmrc\fR
.IP \(bu 4
\fBnode_modules\fR
.IP \(bu 4
\fBpackage-lock.json\fR
.IP \(bu 4
\fBpnpm-lock.yaml\fR
.IP \(bu 4
\fByarn.lock\fR
.IP \(bu 4
\fBbun.lockb\fR
.RE 0

.P
These can not be included.
.SS "exports"
.P
The "exports" provides a modern alternative to "main" allowing multiple entry points to be defined, conditional entry resolution support between environments, and preventing any other entry points besides those defined in "exports". This encapsulation allows module authors to clearly define the public interface for their package. For more details see the \fBnode.js documentation on package entry points\fR \fI\(lahttps://nodejs.org/api/packages.html#package-entry-points\(ra\fR
.SS "main"
.P
The main field is a module ID that is the primary entry point to your program. That is, if your package is named \fBfoo\fR, and a user installs it, and then does \fBrequire("foo")\fR, then your main module's exports object will be returned.
.P
This should be a module relative to the root of your package folder.
.P
For most modules, it makes the most sense to have a main script and often not much else.
.P
If \fBmain\fR is not set, it defaults to \fBindex.js\fR in the package's root folder.
.SS "browser"
.P
If your module is meant to be used client-side the browser field should be used instead of the main field. This is helpful to hint users that it might rely on primitives that aren't available in Node.js modules. (e.g. \fBwindow\fR)
.SS "bin"
.P
A lot of packages have one or more executable files that they'd like to install into the PATH. npm makes this pretty easy (in fact, it uses this feature to install the "npm" executable.)
.P
To use this, supply a \fBbin\fR field in your package.json which is a map of command name to local file name. When this package is installed globally, that file will be either linked inside the global bins directory or a cmd (Windows Command File) will be created which executes the specified file in the \fBbin\fR field, so it is available to run by \fBname\fR or \fBname.cmd\fR (on Windows PowerShell). When this package is installed as a dependency in another package, the file will be linked where it will be available to that package either directly by \fBnpm exec\fR or by name in other scripts when invoking them via \fBnpm run\fR.
.P
For example, myapp could have this:
.P
.RS 2
.nf
{
  "bin": {
    "myapp": "bin/cli.js"
  }
}
.fi
.RE
.P
So, when you install myapp, in case of unix-like OS it'll create a symlink from the \fBcli.js\fR script to \fB/usr/local/bin/myapp\fR and in case of windows it will create a cmd file usually at \fBC:\[rs]Users\[rs]{Username}\[rs]AppData\[rs]Roaming\[rs]npm\[rs]myapp.cmd\fR which runs the \fBcli.js\fR script.
.P
If you have a single executable, and its name should be the name of the package, then you can just supply it as a string. For example:
.P
.RS 2
.nf
{
  "name": "my-program",
  "version": "1.2.5",
  "bin": "path/to/program"
}
.fi
.RE
.P
would be the same as this:
.P
.RS 2
.nf
{
  "name": "my-program",
  "version": "1.2.5",
  "bin": {
    "my-program": "path/to/program"
  }
}
.fi
.RE
.P
Please make sure that your file(s) referenced in \fBbin\fR starts with \fB#!/usr/bin/env node\fR, otherwise the scripts are started without the node executable!
.P
Note that you can also set the executable files using \fBdirectories.bin\fR \fI(directories.bin)\fR.
.P
See \fBfolders\fR \fI\(la/configuring-npm/folders#executables\(ra\fR for more info on executables.
.SS "man"
.P
Specify either a single file or an array of filenames to put in place for the \fBman\fR program to find.
.P
If only a single file is provided, then it's installed such that it is the result from \fBman <pkgname>\fR, regardless of its actual filename. For example:
.P
.RS 2
.nf
{
  "name": "foo",
  "version": "1.2.3",
  "description": "A packaged foo fooer for fooing foos",
  "main": "foo.js",
  "man": "./man/doc.1"
}
.fi
.RE
.P
would link the \fB./man/doc.1\fR file in such that it is the target for \fBman
foo\fR
.P
If the filename doesn't start with the package name, then it's prefixed. So, this:
.P
.RS 2
.nf
{
  "name": "foo",
  "version": "1.2.3",
  "description": "A packaged foo fooer for fooing foos",
  "main": "foo.js",
  "man": \[lB]
    "./man/foo.1",
    "./man/bar.1"
  \[rB]
}
.fi
.RE
.P
will create files to do \fBman foo\fR and \fBman foo-bar\fR.
.P
Man files must end with a number, and optionally a \fB.gz\fR suffix if they are compressed. The number dictates which man section the file is installed into.
.P
.RS 2
.nf
{
  "name": "foo",
  "version": "1.2.3",
  "description": "A packaged foo fooer for fooing foos",
  "main": "foo.js",
  "man": \[lB]
    "./man/foo.1",
    "./man/foo.2"
  \[rB]
}
.fi
.RE
.P
will create entries for \fBman foo\fR and \fBman 2 foo\fR
.SS "directories"
.P
The CommonJS \fBPackages\fR \fI\(lahttp://wiki.commonjs.org/wiki/Packages/1.0\(ra\fR spec details a few ways that you can indicate the structure of your package using a \fBdirectories\fR object. If you look at \fBnpm's package.json\fR \fI\(lahttps://registry.npmjs.org/npm/latest\(ra\fR, you'll see that it has directories for doc, lib, and man.
.P
In the future, this information may be used in other creative ways.
.SS "directories.bin"
.P
If you specify a \fBbin\fR directory in \fBdirectories.bin\fR, all the files in that folder will be added.
.P
Because of the way the \fBbin\fR directive works, specifying both a \fBbin\fR path and setting \fBdirectories.bin\fR is an error. If you want to specify individual files, use \fBbin\fR, and for all the files in an existing \fBbin\fR directory, use \fBdirectories.bin\fR.
.SS "directories.man"
.P
A folder that is full of man pages. Sugar to generate a "man" array by walking the folder.
.SS "repository"
.P
Specify the place where your code lives. This is helpful for people who want to contribute. If the git repo is on GitHub, then the \fBnpm repo\fR command will be able to find you.
.P
Do it like this:
.P
.RS 2
.nf
{
  "repository": {
    "type": "git",
    "url": "git+https://github.com/npm/cli.git"
  }
}
.fi
.RE
.P
The URL should be a publicly available (perhaps read-only) URL that can be handed directly to a VCS program without any modification. It should not be a URL to an html project page that you put in your browser. It's for computers.
.P
For GitHub, GitHub gist, Bitbucket, or GitLab repositories you can use the same shortcut syntax you use for \fBnpm install\fR:
.P
.RS 2
.nf
{
  "repository": "npm/example",

  "repository": "github:npm/example",

  "repository": "gist:11081aaa281",

  "repository": "bitbucket:user/repo",

  "repository": "gitlab:user/repo"
}
.fi
.RE
.P
If the \fBpackage.json\fR for your package is not in the root directory (for example if it is part of a monorepo), you can specify the directory in which it lives:
.P
.RS 2
.nf
{
  "repository": {
    "type": "git",
    "url": "git+https://github.com/npm/cli.git",
    "directory": "workspaces/libnpmpublish"
  }
}
.fi
.RE
.SS "scripts"
.P
The "scripts" property is a dictionary containing script commands that are run at various times in the lifecycle of your package. The key is the lifecycle event, and the value is the command to run at that point.
.P
See npm help scripts to find out more about writing package scripts.
.SS "config"
.P
A "config" object can be used to set configuration parameters used in package scripts that persist across upgrades. For instance, if a package had the following:
.P
.RS 2
.nf
{
  "name": "foo",
  "config": {
    "port": "8080"
  }
}
.fi
.RE
.P
It could also have a "start" command that referenced the \fBnpm_package_config_port\fR environment variable.
.SS "dependencies"
.P
Dependencies are specified in a simple object that maps a package name to a version range. The version range is a string which has one or more space-separated descriptors. Dependencies can also be identified with a tarball or git URL.
.P
\fBPlease do not put test harnesses or transpilers or other "development" time tools in your \fBdependencies\fB object.\fR See \fBdevDependencies\fR, below.
.P
See \fBsemver\fR \fI\(lahttps://github.com/npm/node-semver#versions\(ra\fR for more details about specifying version ranges.
.RS 0
.IP \(bu 4
\fBversion\fR Must match \fBversion\fR exactly
.IP \(bu 4
\fB>version\fR Must be greater than \fBversion\fR
.IP \(bu 4
\fB>=version\fR etc
.IP \(bu 4
\fB<version\fR
.IP \(bu 4
\fB<=version\fR
.IP \(bu 4
\fB~version\fR "Approximately equivalent to version" See \fBsemver\fR \fI\(lahttps://github.com/npm/node-semver#versions\(ra\fR
.IP \(bu 4
\fB^version\fR "Compatible with version" See \fBsemver\fR \fI\(lahttps://github.com/npm/node-semver#versions\(ra\fR
.IP \(bu 4
\fB1.2.x\fR 1.2.0, 1.2.1, etc., but not 1.3.0
.IP \(bu 4
\fBhttp://...\fR See 'URLs as Dependencies' below
.IP \(bu 4
\fB*\fR Matches any version
.IP \(bu 4
\fB""\fR (just an empty string) Same as \fB*\fR
.IP \(bu 4
\fBversion1 - version2\fR Same as \fB>=version1 <=version2\fR.
.IP \(bu 4
\fBrange1 || range2\fR Passes if either range1 or range2 are satisfied.
.IP \(bu 4
\fBgit...\fR See 'Git URLs as Dependencies' below
.IP \(bu 4
\fBuser/repo\fR See 'GitHub URLs' below
.IP \(bu 4
\fBtag\fR A specific version tagged and published as \fBtag\fR See npm help dist-tag
.IP \(bu 4
\fBpath/path/path\fR See \fBLocal Paths\fR \fI(Local Paths)\fR below
.IP \(bu 4
\fBnpm:@scope/pkg@version\fR Custom alias for a package See \fB\fBpackage-spec\fR\fR \fI\(la/using-npm/package-spec#aliases\(ra\fR
.RE 0

.P
For example, these are all valid:
.P
.RS 2
.nf
{
  "dependencies": {
    "foo": "1.0.0 - 2.9999.9999",
    "bar": ">=1.0.2 <2.1.2",
    "baz": ">1.0.2 <=2.3.4",
    "boo": "2.0.1",
    "qux": "<1.0.0 || >=2.3.1 <2.4.5 || >=2.5.2 <3.0.0",
    "asd": "http://npmjs.com/example.tar.gz",
    "til": "~1.2",
    "elf": "~1.2.3",
    "two": "2.x",
    "thr": "3.3.x",
    "lat": "latest",
    "dyl": "file:../dyl",
    "kpg": "npm:pkg@1.0.0"
  }
}
.fi
.RE
.SS "URLs as Dependencies"
.P
You may specify a tarball URL in place of a version range.
.P
This tarball will be downloaded and installed locally to your package at install time.
.SS "Git URLs as Dependencies"
.P
Git URLs are of the form:
.P
.RS 2
.nf
<protocol>://\[lB]<user>\[lB]:<password>\[rB]@\[rB]<hostname>\[lB]:<port>\[rB]\[lB]:\[rB]\[lB]/\[rB]<path>\[lB]#<commit-ish> | #semver:<semver>\[rB]
.fi
.RE
.P
\fB<protocol>\fR is one of \fBgit\fR, \fBgit+ssh\fR, \fBgit+http\fR, \fBgit+https\fR, or \fBgit+file\fR.
.P
If \fB#<commit-ish>\fR is provided, it will be used to clone exactly that commit. If the commit-ish has the format \fB#semver:<semver>\fR, \fB<semver>\fR can be any valid semver range or exact version, and npm will look for any tags or refs matching that range in the remote repository, much as it would for a registry dependency. If neither \fB#<commit-ish>\fR or \fB#semver:<semver>\fR is specified, then the default branch is used.
.P
Examples:
.P
.RS 2
.nf
git+ssh://**************:npm/cli.git#v1.0.27
git+ssh://**************:npm/cli#semver:^5.0
git+https://<EMAIL>/npm/cli.git
git://github.com/npm/cli.git#v1.0.27
.fi
.RE
.P
When installing from a \fBgit\fR repository, the presence of certain fields in the \fBpackage.json\fR will cause npm to believe it needs to perform a build. To do so your repository will be cloned into a temporary directory, all of its deps installed, relevant scripts run, and the resulting directory packed and installed.
.P
This flow will occur if your git dependency uses \fBworkspaces\fR, or if any of the following scripts are present:
.RS 0
.IP \(bu 4
\fBbuild\fR
.IP \(bu 4
\fBprepare\fR
.IP \(bu 4
\fBprepack\fR
.IP \(bu 4
\fBpreinstall\fR
.IP \(bu 4
\fBinstall\fR
.IP \(bu 4
\fBpostinstall\fR
.RE 0

.P
If your git repository includes pre-built artifacts, you will likely want to make sure that none of the above scripts are defined, or your dependency will be rebuilt for every installation.
.SS "GitHub URLs"
.P
As of version 1.1.65, you can refer to GitHub URLs as just "foo": "user/foo-project". Just as with git URLs, a \fBcommit-ish\fR suffix can be included. For example:
.P
.RS 2
.nf
{
  "name": "foo",
  "version": "0.0.0",
  "dependencies": {
    "express": "expressjs/express",
    "mocha": "mochajs/mocha#4727d357ea",
    "module": "npm/example-github-repo#feature\[rs]/branch"
  }
}
.fi
.RE
.SS "Local Paths"
.P
As of version 2.0.0 you can provide a path to a local directory that contains a package. Local paths can be saved using \fBnpm install -S\fR or \fBnpm
install --save\fR, using any of these forms:
.P
.RS 2
.nf
../foo/bar
~/foo/bar
./foo/bar
/foo/bar
.fi
.RE
.P
in which case they will be normalized to a relative path and added to your \fBpackage.json\fR. For example:
.P
.RS 2
.nf
{
  "name": "baz",
  "dependencies": {
    "bar": "file:../foo/bar"
  }
}
.fi
.RE
.P
This feature is helpful for local offline development and creating tests that require npm installing where you don't want to hit an external server, but should not be used when publishing your package to the public registry.
.P
\fInote\fR: Packages linked by local path will not have their own dependencies installed when \fBnpm install\fR is run. You must run \fBnpm install\fR from inside the local path itself.
.SS "devDependencies"
.P
If someone is planning on downloading and using your module in their program, then they probably don't want or need to download and build the external test or documentation framework that you use.
.P
In this case, it's best to map these additional items in a \fBdevDependencies\fR object.
.P
These things will be installed when doing \fBnpm link\fR or \fBnpm install\fR from the root of a package, and can be managed like any other npm configuration param. See npm help config for more on the topic.
.P
For build steps that are not platform-specific, such as compiling CoffeeScript or other languages to JavaScript, use the \fBprepare\fR script to do this, and make the required package a devDependency.
.P
For example:
.P
.RS 2
.nf
{
  "name": "@npm/ethopia-waza",
  "description": "a delightfully fruity coffee varietal",
  "version": "1.2.3",
  "devDependencies": {
    "coffee-script": "~1.6.3"
  },
  "scripts": {
    "prepare": "coffee -o lib/ -c src/waza.coffee"
  },
  "main": "lib/waza.js"
}
.fi
.RE
.P
The \fBprepare\fR script will be run before publishing, so that users can consume the functionality without requiring them to compile it themselves. In dev mode (ie, locally running \fBnpm install\fR), it'll run this script as well, so that you can test it easily.
.SS "peerDependencies"
.P
In some cases, you want to express the compatibility of your package with a host tool or library, while not necessarily doing a \fBrequire\fR of this host. This is usually referred to as a \fIplugin\fR. Notably, your module may be exposing a specific interface, expected and specified by the host documentation.
.P
For example:
.P
.RS 2
.nf
{
  "name": "@npm/tea-latte",
  "version": "1.3.5",
  "peerDependencies": {
    "@npm/tea": "2.x"
  }
}
.fi
.RE
.P
This ensures your package \fB@npm/tea-latte\fR can be installed \fIalong\fR with the second major version of the host package \fB@npm/tea\fR only. \fBnpm install
tea-latte\fR could possibly yield the following dependency graph:
.P
.RS 2
.nf
├── @npm/tea-latte@1.3.5
└── @npm/tea@2.2.0
.fi
.RE
.P
In npm versions 3 through 6, \fBpeerDependencies\fR were not automatically installed, and would raise a warning if an invalid version of the peer dependency was found in the tree. As of npm v7, peerDependencies \fIare\fR installed by default.
.P
Trying to install another plugin with a conflicting requirement may cause an error if the tree cannot be resolved correctly. For this reason, make sure your plugin requirement is as broad as possible, and not to lock it down to specific patch versions.
.P
Assuming the host complies with \fBsemver\fR \fI\(lahttps://semver.org/\(ra\fR, only changes in the host package's major version will break your plugin. Thus, if you've worked with every 1.x version of the host package, use \fB"^1.0"\fR or \fB"1.x"\fR to express this. If you depend on features introduced in 1.5.2, use \fB"^1.5.2"\fR.
.SS "peerDependenciesMeta"
.P
The \fBpeerDependenciesMeta\fR field serves to provide npm more information on how your peer dependencies are to be used. Specifically, it allows peer dependencies to be marked as optional. Npm will not automatically install optional peer dependencies. This allows you to integrate and interact with a variety of host packages without requiring all of them to be installed.
.P
For example:
.P
.RS 2
.nf
{
  "name": "@npm/tea-latte",
  "version": "1.3.5",
  "peerDependencies": {
    "@npm/tea": "2.x",
    "@npm/soy-milk": "1.2"
  },
  "peerDependenciesMeta": {
    "@npm/soy-milk": {
      "optional": true
    }
  }
}
.fi
.RE
.SS "bundleDependencies"
.P
This defines an array of package names that will be bundled when publishing the package.
.P
In cases where you need to preserve npm packages locally or have them available through a single file download, you can bundle the packages in a tarball file by specifying the package names in the \fBbundleDependencies\fR array and executing \fBnpm pack\fR.
.P
For example:
.P
If we define a package.json like this:
.P
.RS 2
.nf
{
  "name": "@npm/awesome-web-framework",
  "version": "1.0.0",
  "bundleDependencies": \[lB]
    "@npm/renderized",
    "@npm/super-streams"
  \[rB]
}
.fi
.RE
.P
we can obtain \fB@npm/awesome-web-framework-1.0.0.tgz\fR file by running \fBnpm pack\fR. This file contains the dependencies \fB@npm/renderized\fR and \fB@npm/super-streams\fR which can be installed in a new project by executing \fBnpm install
awesome-web-framework-1.0.0.tgz\fR. Note that the package names do not include any versions, as that information is specified in \fBdependencies\fR.
.P
If this is spelled \fB"bundledDependencies"\fR, then that is also honored.
.P
Alternatively, \fB"bundleDependencies"\fR can be defined as a boolean value. A value of \fBtrue\fR will bundle all dependencies, a value of \fBfalse\fR will bundle none.
.SS "optionalDependencies"
.P
If a dependency can be used, but you would like npm to proceed if it cannot be found or fails to install, then you may put it in the \fBoptionalDependencies\fR object. This is a map of package name to version or URL, just like the \fBdependencies\fR object. The difference is that build failures do not cause installation to fail. Running \fBnpm install
--omit=optional\fR will prevent these dependencies from being installed.
.P
It is still your program's responsibility to handle the lack of the dependency. For example, something like this:
.P
.RS 2
.nf
try {
  var foo = require('@npm/foo')
  var fooVersion = require('@npm/foo/package.json').version
} catch (er) {
  foo = null
}
if ( notGoodFooVersion(fooVersion) ) {
  foo = null
}

// .. then later in your program ..

if (foo) {
  foo.doFooThings()
}
.fi
.RE
.P
Entries in \fBoptionalDependencies\fR will override entries of the same name in \fBdependencies\fR, so it's usually best to only put in one place.
.SS "overrides"
.P
If you need to make specific changes to dependencies of your dependencies, for example replacing the version of a dependency with a known security issue, replacing an existing dependency with a fork, or making sure that the same version of a package is used everywhere, then you may add an override.
.P
Overrides provide a way to replace a package in your dependency tree with another version, or another package entirely. These changes can be scoped as specific or as vague as desired.
.P
Overrides are only considered in the root \fBpackage.json\fR file for a project. Overrides in installed dependencies (including npm help workspaces) are not considered in dependency tree resolution. Published packages may dictate their resolutions by pinning dependencies or using an \fB\fBnpm-shrinkwrap.json\fR\fR \fI\(la/configuring-npm/npm-shrinkwrap-json\(ra\fR file.
.P
To make sure the package \fB@npm/foo\fR is always installed as version \fB1.0.0\fR no matter what version your dependencies rely on:
.P
.RS 2
.nf
{
  "overrides": {
    "@npm/foo": "1.0.0"
  }
}
.fi
.RE
.P
The above is a short hand notation, the full object form can be used to allow overriding a package itself as well as a child of the package. This will cause \fB@npm/foo\fR to always be \fB1.0.0\fR while also making \fB@npm/bar\fR at any depth beyond \fB@npm/foo\fR also \fB1.0.0\fR:
.P
.RS 2
.nf
{
  "overrides": {
    "@npm/foo": {
      ".": "1.0.0",
      "@npm/bar": "1.0.0"
    }
  }
}
.fi
.RE
.P
To only override \fB@npm/foo\fR to be \fB1.0.0\fR when it's a child (or grandchild, or great grandchild, etc) of the package \fB@npm/bar\fR:
.P
.RS 2
.nf
{
  "overrides": {
    "@npm/bar": {
      "@npm/foo": "1.0.0"
    }
  }
}
.fi
.RE
.P
Keys can be nested to any arbitrary length. To override \fB@npm/foo\fR only when it's a child of \fB@npm/bar\fR and only when \fB@npm/bar\fR is a child of \fB@npm/baz\fR:
.P
.RS 2
.nf
{
  "overrides": {
    "@npm/baz": {
      "@npm/bar": {
        "@npm/foo": "1.0.0"
      }
    }
  }
}
.fi
.RE
.P
The key of an override can also include a version, or range of versions. To override \fB@npm/foo\fR to \fB1.0.0\fR, but only when it's a child of \fB@npm/bar@2.0.0\fR:
.P
.RS 2
.nf
{
  "overrides": {
    "@npm/bar@2.0.0": {
      "@npm/foo": "1.0.0"
    }
  }
}
.fi
.RE
.P
You may not set an override for a package that you directly depend on unless both the dependency and the override itself share the exact same spec. To make this limitation easier to deal with, overrides may also be defined as a reference to a spec for a direct dependency by prefixing the name of the package you wish the version to match with a \fB$\fR.
.P
.RS 2
.nf
{
  "dependencies": {
    "@npm/foo": "^1.0.0"
  },
  "overrides": {
    // BAD, will throw an EOVERRIDE error
    // "foo": "^2.0.0"
    // GOOD, specs match so override is allowed
    // "foo": "^1.0.0"
    // BEST, the override is defined as a reference to the dependency
    "@npm/foo": "$foo",
    // the referenced package does not need to match the overridden one
    "@npm/bar": "$foo"
  }
}
.fi
.RE
.SS "engines"
.P
You can specify the version of node that your stuff works on:
.P
.RS 2
.nf
{
  "engines": {
    "node": ">=0.10.3 <15"
  }
}
.fi
.RE
.P
And, like with dependencies, if you don't specify the version (or if you specify "*" as the version), then any version of node will do.
.P
You can also use the "engines" field to specify which versions of npm are capable of properly installing your program. For example:
.P
.RS 2
.nf
{
  "engines": {
    "npm": "~1.0.20"
  }
}
.fi
.RE
.P
Unless the user has set the \fB\fBengine-strict\fR config\fR \fI\(la/using-npm/config#engine-strict\(ra\fR flag, this field is advisory only and will only produce warnings when your package is installed as a dependency.
.SS "os"
.P
You can specify which operating systems your module will run on:
.P
.RS 2
.nf
{
  "os": \[lB]
    "darwin",
    "linux"
  \[rB]
}
.fi
.RE
.P
You can also block instead of allowing operating systems, just prepend the blocked os with a '!':
.P
.RS 2
.nf
{
  "os": \[lB]
    "!win32"
  \[rB]
}
.fi
.RE
.P
The host operating system is determined by \fBprocess.platform\fR
.P
It is allowed to both block and allow an item, although there isn't any good reason to do this.
.SS "cpu"
.P
If your code only runs on certain cpu architectures, you can specify which ones.
.P
.RS 2
.nf
{
  "cpu": \[lB]
    "x64",
    "ia32"
  \[rB]
}
.fi
.RE
.P
Like the \fBos\fR option, you can also block architectures:
.P
.RS 2
.nf
{
  "cpu": \[lB]
    "!arm",
    "!mips"
  \[rB]
}
.fi
.RE
.P
The host architecture is determined by \fBprocess.arch\fR
.SS "libc"
.P
If your code only runs or builds in certain versions of libc, you can specify which ones. This field only applies if \fBos\fR is \fBlinux\fR.
.P
.RS 2
.nf
{
  "os": "linux",
  "libc": "glibc"
}
.fi
.RE
.SS "devEngines"
.P
The \fBdevEngines\fR field aids engineers working on a codebase to all be using the same tooling.
.P
You can specify a \fBdevEngines\fR property in your \fBpackage.json\fR which will run before \fBinstall\fR, \fBci\fR, and \fBrun\fR commands.
.RS 0
.P
Note: \fBengines\fR and \fBdevEngines\fR differ in object shape. They also function very differently. \fBengines\fR is designed to alert the user when a dependency uses a differening npm or node version that the project it's being used in, whereas \fBdevEngines\fR is used to alert people interacting with the source code of a project.
.RE 0

.P
The supported keys under the \fBdevEngines\fR property are \fBcpu\fR, \fBos\fR, \fBlibc\fR, \fBruntime\fR, and \fBpackageManager\fR. Each property can be an object or an array of objects. Objects must contain \fBname\fR, and optionally can specify \fBversion\fR, and \fBonFail\fR. \fBonFail\fR can be \fBwarn\fR, \fBerror\fR, or \fBignore\fR, and if left undefined is of the same value as \fBerror\fR. \fBnpm\fR will assume that you're running with \fBnode\fR. Here's an example of a project that will fail if the environment is not \fBnode\fR and \fBnpm\fR. If you set \fBruntime.name\fR or \fBpackageManager.name\fR to any other string, it will fail within the npm CLI.
.P
.RS 2
.nf
{
  "devEngines": {
    "runtime": {
      "name": "node",
      "onFail": "error"
    },
    "packageManager": {
      "name": "npm",
      "onFail": "error"
    }
  }
}
.fi
.RE
.SS "private"
.P
If you set \fB"private": true\fR in your package.json, then npm will refuse to publish it.
.P
This is a way to prevent accidental publication of private repositories. If you would like to ensure that a given package is only ever published to a specific registry (for example, an internal registry), then use the \fBpublishConfig\fR dictionary described below to override the \fBregistry\fR config param at publish-time.
.SS "publishConfig"
.P
This is a set of config values that will be used at publish-time. It's especially handy if you want to set the tag, registry or access, so that you can ensure that a given package is not tagged with "latest", published to the global public registry or that a scoped module is private by default.
.P
See npm help config to see the list of config options that can be overridden.
.SS "workspaces"
.P
The optional \fBworkspaces\fR field is an array of file patterns that describes locations within the local file system that the install client should look up to find each npm help workspace that needs to be symlinked to the top level \fBnode_modules\fR folder.
.P
It can describe either the direct paths of the folders to be used as workspaces or it can define globs that will resolve to these same folders.
.P
In the following example, all folders located inside the folder \fB./packages\fR will be treated as workspaces as long as they have valid \fBpackage.json\fR files inside them:
.P
.RS 2
.nf
{
  "name": "workspace-example",
  "workspaces": \[lB]
    "./packages/*"
  \[rB]
}
.fi
.RE
.P
See npm help workspaces for more examples.
.SS "DEFAULT VALUES"
.P
npm will default some values based on package contents.
.RS 0
.IP \(bu 4
\fB"scripts": {"start": "node server.js"}\fR
.P
If there is a \fBserver.js\fR file in the root of your package, then npm will default the \fBstart\fR command to \fBnode server.js\fR.
.IP \(bu 4
\fB"scripts":{"install": "node-gyp rebuild"}\fR
.P
If there is a \fBbinding.gyp\fR file in the root of your package and you have not defined an \fBinstall\fR or \fBpreinstall\fR script, npm will default the \fBinstall\fR command to compile using node-gyp.
.IP \(bu 4
\fB"contributors": \[lB]...\[rB]\fR
.P
If there is an \fBAUTHORS\fR file in the root of your package, npm will treat each line as a \fBName <email> (url)\fR format, where email and url are optional. Lines which start with a \fB#\fR or are blank, will be ignored.
.RE 0

.SS "SEE ALSO"
.RS 0
.IP \(bu 4
\fBsemver\fR \fI\(lahttps://github.com/npm/node-semver#versions\(ra\fR
.IP \(bu 4
npm help workspaces
.IP \(bu 4
npm help init
.IP \(bu 4
npm help version
.IP \(bu 4
npm help config
.IP \(bu 4
npm help help
.IP \(bu 4
npm help install
.IP \(bu 4
npm help publish
.IP \(bu 4
npm help uninstall
.RE 0
