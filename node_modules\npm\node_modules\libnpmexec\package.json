{"name": "libnpmexec", "version": "10.1.2", "files": ["bin/", "lib/"], "main": "lib/index.js", "engines": {"node": "^20.17.0 || >=22.9.0"}, "description": "npm exec (npx) programmatic API", "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmexec"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "workspaces", "libnpmexec"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"lint": "npm run eslint", "posttest": "npm run lint", "test": "tap", "snap": "tap", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"files": "test/*.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "@npmcli/template-oss": "4.23.6", "bin-links": "^5.0.0", "chalk": "^5.2.0", "just-extend": "^6.2.0", "just-safe-set": "^4.2.1", "tap": "^16.3.8"}, "dependencies": {"@npmcli/arborist": "^9.1.0", "@npmcli/package-json": "^6.1.1", "@npmcli/run-script": "^9.0.1", "ci-info": "^4.0.0", "npm-package-arg": "^12.0.0", "pacote": "^21.0.0", "proc-log": "^5.0.0", "read": "^4.0.0", "read-package-json-fast": "^4.0.0", "semver": "^7.3.7", "walk-up-path": "^4.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}}