{"name": "just-diff", "version": "6.0.2", "description": "Return an object representing the diffs between two objects. Supports jsonPatch protocol", "type": "module", "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./package.json": "./package.json"}, "main": "index.cjs", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c"}, "repository": "https://github.com/angus-c/just", "keywords": ["object", "diff", "jsonPatch", "no-dependencies", "just"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/angus-c/just/issues"}}