#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞检违规反馈 - 构建配置生成器
专门为飞检违规反馈功能生成PyInstaller spec文件
"""

import os
import sys
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    return Path(__file__).parent.absolute()

def collect_data_files():
    """收集需要打包的数据文件"""
    project_root = get_project_root()
    data_files = []
    
    # HTML模板文件 - 飞检违规反馈页面
    page_dir = project_root / 'page'
    if page_dir.exists():
        # 只包含飞检违规反馈相关的页面
        violation_files = ['violation_feedback.html', 'index.html']
        for html_file in page_dir.glob('*.html'):
            if html_file.name in violation_files:
                data_files.append((str(html_file), 'page'))
    
    # 静态资源文件
    static_dir = project_root / 'static'
    if static_dir.exists():
        for static_file in static_dir.rglob('*'):
            if static_file.is_file():
                rel_path = static_file.relative_to(project_root)
                data_files.append((str(static_file), str(rel_path.parent)))
    
    # 配置文件
    config_file = project_root / 'config.ini'
    if config_file.exists():
        data_files.append((str(config_file), '.'))
    
    # 输出目录（创建空目录）
    output_dir = project_root / 'output'
    if output_dir.exists():
        data_files.append((str(output_dir), 'output'))
    
    # 上传目录（创建空目录）
    uploads_dir = project_root / 'uploads'
    if uploads_dir.exists():
        data_files.append((str(uploads_dir), 'uploads'))
    
    return data_files

def collect_hidden_imports():
    """收集隐藏导入"""
    hidden_imports = [
        'flask',
        'flask.templating',
        'jinja2',
        'werkzeug',
        'werkzeug.serving',
        'werkzeug.utils',
        'json',
        'logging',
        'threading',
        'queue',
        'datetime',
        'uuid',
        'pathlib',
        'sqlite3',
        'encodings.utf_8',
        'encodings.gbk',
        'encodings.cp936',
        'pandas',
        'openpyxl',
        'random',
        'time',
        'os',
        'random_sampler'
    ]

    # 尝试导入数据库模块，如果可用则添加
    try:
        import psycopg2
        hidden_imports.extend([
            'psycopg2',
            'psycopg2._psycopg',
            'psycopg2.extensions'
        ])
    except ImportError:
        print("警告: psycopg2 未安装，跳过相关导入")

    try:
        import oracledb
        hidden_imports.append('oracledb')
    except ImportError:
        print("警告: oracledb 未安装，跳过相关导入")

    try:
        import psycopg
        hidden_imports.append('psycopg')
    except ImportError:
        print("警告: psycopg 未安装，跳过相关导入")

    return hidden_imports

def generate_spec_file():
    """生成PyInstaller spec文件"""
    project_root = get_project_root()
    project_name = "飞检违规反馈"
    
    data_files = collect_data_files()
    hidden_imports = collect_hidden_imports()
    
    # 构建datas列表字符串
    datas_str = "[\n"
    for src, dst in data_files:
        datas_str += f"    (r'{src}', r'{dst}'),\n"
    datas_str += "]"
    
    # 构建hiddenimports列表字符串
    hidden_imports_str = "[\n"
    for imp in hidden_imports:
        hidden_imports_str += f"    '{imp}',\n"
    hidden_imports_str += "]"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[r'{project_root}'],
    binaries=[],
    datas={datas_str},
    hiddenimports={hidden_imports_str},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
        'fastapi',
        'uvicorn',
        'gunicorn',
        'redis',
        'aioredis',
        'asyncpg',
        'aiomysql',
        'aiohttp',
        'requests',
        'jieba',
        'structlog',
        'prometheus_client',
        'ujson',
        'orjson'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{project_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{project_name}_new',
)
'''
    
    spec_file_path = project_root / 'violation_feedback.spec'
    with open(spec_file_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"Success: PyInstaller spec文件已生成: {spec_file_path}")
    print(f"Success: 收集到 {len(data_files)} 个数据文件")
    print(f"Success: 配置了 {len(hidden_imports)} 个隐藏导入")
    
    return spec_file_path

def create_startup_script():
    """创建启动脚本"""
    project_root = get_project_root()
    startup_script = project_root / 'dist' / '飞检违规反馈' / 'start.bat'

    # 确保目录存在
    startup_script.parent.mkdir(parents=True, exist_ok=True)

    script_content = '''@echo off
chcp 65001 > nul
echo ========================================
echo 飞检违规反馈工具
echo ========================================
echo.
echo 正在启动应用...
echo 应用将在浏览器中打开: http://localhost:5000
echo.
echo 注意事项:
echo - 请勿关闭此命令行窗口
echo - 关闭此窗口将停止应用
echo - 如需停止应用，请按 Ctrl+C
echo.

:: 等待2秒后打开浏览器
timeout /t 2 /nobreak > nul
start "" "http://localhost:5000"

echo 启动中，请稍候...
echo.
"飞检违规反馈.exe"

echo.
echo 应用已停止
pause
'''

    with open(startup_script, 'w', encoding='utf-8') as f:
        f.write(script_content)

    print(f"Success: 启动脚本已创建: {startup_script}")

def create_readme_file():
    """创建部署说明文件"""
    project_root = get_project_root()
    readme_file = project_root / 'dist' / '飞检违规反馈' / 'README.txt'

    # 确保目录存在
    readme_file.parent.mkdir(parents=True, exist_ok=True)

    readme_content = '''飞检违规反馈工具 - 使用说明
========================================

快速开始:
1. 双击 "飞检违规反馈.exe" 启动应用
2. 或双击 "start.bat" 启动（推荐，会自动打开浏览器）
3. 在浏览器中访问 http://localhost:5000
4. 点击"飞检违规反馈"功能模块

功能说明:
- 随机病案抽取：从指定文件夹中随机抽取病案进行审查
- 违规问题记录：记录发现的违规问题
- 反馈报告生成：生成违规反馈报告
- Excel文件处理：支持批量处理Excel文件

使用步骤:
1. 准备源数据文件夹（包含Excel文件）
2. 设置输出文件夹路径
3. 配置抽取数量和其他参数
4. 执行随机抽取
5. 查看抽取结果和汇总报告

注意事项:
- 首次启动可能需要较长时间，请耐心等待
- 请确保5000端口未被其他程序占用
- 关闭命令行窗口将停止应用
- 建议使用Chrome、Edge或Firefox浏览器
- 源文件应包含"结算单据号"列

故障排除:
- 如果无法启动，请检查Windows防火墙设置
- 如果端口被占用，请关闭占用5000端口的其他程序
- 如遇到其他问题，请查看命令行窗口的错误信息

技术支持:
如需技术支持，请提供完整的错误信息和系统环境信息。

版本信息:
构建时间: {build_time}
Python版本: {python_version}
'''

    import datetime
    import sys

    formatted_content = readme_content.format(
        build_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        python_version=sys.version.split()[0]
    )

    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(formatted_content)

    print(f"Success: 部署说明文件已创建: {readme_file}")

def main():
    """主函数"""
    print("========================================")
    print("飞检违规反馈工具 - 构建配置生成器")
    print("========================================")
    print()

    try:
        # 生成spec文件
        spec_file = generate_spec_file()

        # 创建启动脚本
        create_startup_script()

        # 创建说明文件
        create_readme_file()

        print()
        print("Success: 构建配置生成完成！")
        print("现在可以运行 PyInstaller 进行打包")

    except Exception as e:
        print(f"Error: 配置生成失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 