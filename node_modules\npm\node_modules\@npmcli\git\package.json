{"name": "@npmcli/git", "version": "6.0.3", "main": "lib/index.js", "files": ["bin/", "lib/"], "description": "a util for spawning git from npm CLI contexts", "repository": {"type": "git", "url": "git+https://github.com/npm/git.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"timeout": 600, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.24.1", "npm-package-arg": "^12.0.1", "slash": "^3.0.0", "tap": "^16.0.1"}, "dependencies": {"@npmcli/promise-spawn": "^8.0.0", "ini": "^5.0.0", "lru-cache": "^10.0.1", "npm-pick-manifest": "^10.0.0", "proc-log": "^5.0.0", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^5.0.0"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.24.1", "publish": true}}