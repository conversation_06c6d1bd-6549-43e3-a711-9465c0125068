# Implementation Plan

## 1. 修复所属领域字段保存问题 ✅

### 1.1 分析并修复表单数据收集逻辑 ✅
- ✅ 检查 `saveRule()` 函数中的表单数据收集代码（约第1200-1300行）
- ✅ 识别"所属领域"字段被错误排除的具体位置 - 发现问题在后端API
- ✅ 修复后端API `/api/rules/<int:rule_id>` 的字段映射，添加"所属领域"字段
- ✅ 确保该字段被正确包含在主表单数据对象中
- _Requirements: 1.1, 1.2, 1.3, 1.4_

### 1.2 验证字段映射和数据传递
- 检查表单序列化逻辑，确保"所属领域"字段被正确收集
- 验证字段名称映射的一致性
- 测试默认值"通用"的设置逻辑
- 确保数据正确传递给后端API
- _Requirements: 1.1, 1.4_

### 1.3 添加字段保存验证和错误处理
- 在保存前验证"所属领域"字段是否被正确收集
- 添加字段缺失时的错误提示
- 实现保存失败时的详细错误信息显示
- _Requirements: 3.1, 3.4_

## 2. 修复批量AI智能获取功能的规则内涵丢失问题 ✅

### 2.1 分析批量保存数据处理逻辑 ✅
- ✅ 检查 `saveBatchResults()` 函数中的数据处理代码（约第2800-3000行）
- ✅ 识别导致"规则内涵"字段丢失的具体代码位置 - 批量更新API缺少规则内涵字段映射
- ✅ 分析当前的数据更新策略（替换 vs 合并）
- _Requirements: 2.1, 2.2_

### 2.2 实现数据合并逻辑以保留现有字段 ✅
- ✅ 在批量更新前获取现有规则的完整数据 - 通过ruleData.ruleContent获取
- ✅ 修复后端API `/api/rules/batch-update-ai-fields/<int:rule_id>` 添加规则内涵字段映射
- ✅ 确保"规则内涵"等关键字段在合并过程中被保留
- ✅ 修改前端调用以发送完整的合并数据包含规则内涵
- _Requirements: 2.1, 2.3, 2.4_

### 2.3 优化批量处理的字段更新策略
- 明确定义哪些字段由AI更新，哪些字段需要保留
- 实现选择性字段更新逻辑
- 确保批量操作只更新指定的AI分析字段
- _Requirements: 2.2, 3.2, 3.3_

### 2.4 添加批量操作的数据完整性检查
- 在批量保存前验证关键字段的存在性
- 实现数据丢失检测和警告机制
- 添加批量操作失败时的数据恢复功能
- _Requirements: 3.1, 3.3, 3.4_

## 3. 代码质量改进和测试

### 3.1 重构表单数据处理函数
- 提取公共的字段收集逻辑
- 创建统一的字段验证函数
- 改进代码可读性和维护性
- _Requirements: 3.1_

### 3.2 添加调试和日志功能
- 在关键数据处理点添加console.log输出
- 实现字段收集状态的详细日志记录
- 添加数据保存前后的状态对比日志
- _Requirements: 3.4_

### 3.3 创建单元测试用例
- 测试"所属领域"字段的保存和读取
- 测试批量AI处理的数据保留功能
- 测试各种边界情况和错误场景
- 验证修复后的功能完整性
- _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4_

## 4. 部署和验证

### 4.1 执行端到端测试
- 测试完整的规则创建和编辑流程
- 验证批量AI智能获取功能的数据完整性
- 测试各种用户操作场景
- _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 2.4_

### 4.2 性能和稳定性验证
- 验证修复不会影响页面性能
- 测试大量数据的批量处理稳定性
- 确保修复不会引入新的问题
- _Requirements: 3.1, 3.2, 3.3_