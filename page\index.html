<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞检数据处理工具箱</title>
    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --hover-bg: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background-color: var(--card-bg);
            padding: 2rem 1.5rem;
            height: 100vh;
            position: fixed;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0,0,0,0.06);
        }

        .sidebar h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            padding: 0 0.5rem;
        }

        .sidebar ul {
            list-style-type: none;
        }

        .sidebar li {
            margin-bottom: 0.5rem;
        }

        .sidebar a {
            text-decoration: none;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .sidebar a:hover {
            background-color: var(--hover-bg);
            color: var(--text-primary);
        }

        .sidebar a i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .main-content {
            margin-left: 280px;
            flex-grow: 1;
            padding: 2rem;
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .tool-item {
            background-color: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease;
            border: 1px solid rgba(0,0,0,0.06);
            position: relative;
            overflow: hidden;
        }

        .tool-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            border-color: var(--primary-color);
        }

        .tool-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .tool-item:hover::before {
            opacity: 1;
        }

        .tool-item i {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .tool-item h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .tool-item p {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
                padding: 1.5rem 0.75rem;
            }
            
            .sidebar h2 {
                display: none;
            }
            
            .sidebar a span {
                display: none;
            }
            
            .main-content {
                margin-left: 80px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="sidebar">
        <h2>数据处理工具箱</h2>
        <nav>
            <ul>
                <li><a href=""><i class="fas fa-home"></i><span>主页</span></a></li>
                <li><a href="violation_feedback"><i class="fas fa-shield-exclamation"></i><span>飞检违规反馈</span></a></li>

            </ul>
        </nav>
    </div>
    <div class="main-content">
        <h1>工具列表</h1>
        <div class="tool-grid">
         
            <a href="violation_feedback" class="tool-item">
                <i class="fas fa-shield-exclamation"></i>
                <h3>飞检违规反馈</h3>
                <p>汇总飞检Excel文件规则详情，统计违规数量和金额，生成违规反馈报告</p>
            </a>

        </div>
    </div>
</body>
</html>
