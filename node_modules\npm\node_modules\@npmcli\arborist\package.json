{"name": "@npmcli/arborist", "version": "9.1.0", "description": "Manage node_modules trees", "dependencies": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/fs": "^4.0.0", "@npmcli/installed-package-contents": "^3.0.0", "@npmcli/map-workspaces": "^4.0.1", "@npmcli/metavuln-calculator": "^9.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "@npmcli/run-script": "^9.0.1", "bin-links": "^5.0.0", "cacache": "^19.0.1", "common-ancestor-path": "^1.0.1", "hosted-git-info": "^8.0.0", "json-stringify-nice": "^1.1.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "nopt": "^8.0.0", "npm-install-checks": "^7.1.0", "npm-package-arg": "^12.0.0", "npm-pick-manifest": "^10.0.0", "npm-registry-fetch": "^18.0.1", "pacote": "^21.0.0", "parse-conflict-json": "^4.0.0", "proc-log": "^5.0.0", "proggy": "^3.0.0", "promise-all-reject-late": "^1.0.0", "promise-call-limit": "^3.0.1", "read-package-json-fast": "^4.0.0", "semver": "^7.3.7", "ssri": "^12.0.0", "treeverse": "^3.0.0", "walk-up-path": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "@npmcli/template-oss": "4.23.6", "benchmark": "^2.1.4", "minify-registry-metadata": "^4.0.0", "nock": "^13.3.3", "tap": "^16.3.8", "tar-stream": "^3.0.0", "tcompare": "^5.0.6"}, "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "test-proxy": "ARBORIST_TEST_PROXY=1 tap --snapshot", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "benchmark": "node scripts/benchmark.js", "benchclean": "rm -rf scripts/benchmark/*/", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/arborist"}, "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "main": "lib/index.js", "bin": {"arborist": "bin/index.js"}, "tap": {"after": "test/fixtures/cleanup.js", "test-env": ["LC_ALL=sk"], "timeout": "720", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}}