.TH "NPM-PKG" "1" "May 2025" "NPM@11.4.0" ""
.SH "NAME"
\fBnpm-pkg\fR - Manages your package.json
.SS "Synopsis"
.P
.RS 2
.nf
npm pkg set <key>=<value> \[lB]<key>=<value> ...\[rB]
npm pkg get \[lB]<key> \[lB]<key> ...\[rB]\[rB]
npm pkg delete <key> \[lB]<key> ...\[rB]
npm pkg set \[lB]<array>\[lB]<index>\[rB].<key>=<value> ...\[rB]
npm pkg set \[lB]<array>\[lB]\[rB].<key>=<value> ...\[rB]
npm pkg fix
.fi
.RE
.SS "Description"
.P
A command that automates the management of \fBpackage.json\fR files. \fBnpm pkg\fR provide 3 different sub commands that allow you to modify or retrieve values for given object keys in your \fBpackage.json\fR.
.P
The syntax to retrieve and set fields is a dot separated representation of the nested object properties to be found within your \fBpackage.json\fR, it's the same notation used in npm help view to retrieve information from the registry manifest, below you can find more examples on how to use it.
.P
Returned values are always in \fBjson\fR format.
.RS 0
.IP \(bu 4
\fBnpm pkg get <field>\fR
.P
Retrieves a value \fBkey\fR, defined in your \fBpackage.json\fR file.
.P
For example, in order to retrieve the name of the current package, you can run:
.P
.RS 2
.nf
npm pkg get name
.fi
.RE
.P
It's also possible to retrieve multiple values at once:
.P
.RS 2
.nf
npm pkg get name version
.fi
.RE
.P
You can view child fields by separating them with a period. To retrieve the value of a test \fBscript\fR value, you would run the following command:
.P
.RS 2
.nf
npm pkg get scripts.test
.fi
.RE
.P
For fields that are arrays, requesting a non-numeric field will return all of the values from the objects in the list. For example, to get all the contributor emails for a package, you would run:
.P
.RS 2
.nf
npm pkg get contributors.email
.fi
.RE
.P
You may also use numeric indices in square braces to specifically select an item in an array field. To just get the email address of the first contributor in the list, you can run:
.P
.RS 2
.nf
npm pkg get contributors\[lB]0\[rB].email
.fi
.RE
.P
For complex fields you can also name a property in square brackets to specifically select a child field. This is especially helpful with the exports object:
.P
.RS 2
.nf
npm pkg get "exports\[lB].\[rB].require"
.fi
.RE
.IP \(bu 4
\fBnpm pkg set <field>=<value>\fR
.P
Sets a \fBvalue\fR in your \fBpackage.json\fR based on the \fBfield\fR value. When saving to your \fBpackage.json\fR file the same set of rules used during \fBnpm install\fR and other cli commands that touches the \fBpackage.json\fR file are used, making sure to respect the existing indentation and possibly applying some validation prior to saving values to the file.
.P
The same syntax used to retrieve values from your package can also be used to define new properties or overriding existing ones, below are some examples of how the dot separated syntax can be used to edit your \fBpackage.json\fR file.
.P
Defining a new bin named \fBmynewcommand\fR in your \fBpackage.json\fR that points to a file \fBcli.js\fR:
.P
.RS 2
.nf
npm pkg set bin.mynewcommand=cli.js
.fi
.RE
.P
Setting multiple fields at once is also possible:
.P
.RS 2
.nf
npm pkg set description='Awesome package' engines.node='>=10'
.fi
.RE
.P
It's also possible to add to array values, for example to add a new contributor entry:
.P
.RS 2
.nf
npm pkg set contributors\[lB]0\[rB].name='Foo' contributors\[lB]0\[rB].email='<EMAIL>'
.fi
.RE
.P
You may also append items to the end of an array using the special empty bracket notation:
.P
.RS 2
.nf
npm pkg set contributors\[lB]\[rB].name='Foo' contributors\[lB]\[rB].name='Bar'
.fi
.RE
.P
It's also possible to parse values as json prior to saving them to your \fBpackage.json\fR file, for example in order to set a \fB"private": true\fR property:
.P
.RS 2
.nf
npm pkg set private=true --json
.fi
.RE
.P
It also enables saving values as numbers:
.P
.RS 2
.nf
npm pkg set tap.timeout=60 --json
.fi
.RE
.IP \(bu 4
\fBnpm pkg delete <key>\fR
.P
Deletes a \fBkey\fR from your \fBpackage.json\fR
.P
The same syntax used to set values from your package can also be used to remove existing ones. For example, in order to remove a script named build:
.P
.RS 2
.nf
npm pkg delete scripts.build
.fi
.RE
.IP \(bu 4
\fBnpm pkg fix\fR
.P
Auto corrects common errors in your \fBpackage.json\fR. npm already does this during \fBpublish\fR, which leads to subtle (mostly harmless) differences between the contents of your \fBpackage.json\fR file and the manifest that npm uses during installation.
.RE 0

.SS "Workspaces support"
.P
You can set/get/delete items across your configured workspaces by using the \fB\fBworkspace\fR\fR \fI\(la/using-npm/config#workspace\(ra\fR or \fB\fBworkspaces\fR\fR \fI\(la/using-npm/config#workspaces\(ra\fR config options.
.P
For example, setting a \fBfunding\fR value across all configured workspaces of a project:
.P
.RS 2
.nf
npm pkg set funding=https://example.com --ws
.fi
.RE
.P
When using \fBnpm pkg get\fR to retrieve info from your configured workspaces, the returned result will be in a json format in which top level keys are the names of each workspace, the values of these keys will be the result values returned from each of the configured workspaces, e.g:
.P
.RS 2
.nf
npm pkg get name version --ws
{
  "a": {
    "name": "a",
    "version": "1.0.0"
  },
  "b": {
    "name": "b",
    "version": "1.0.0"
  }
}
.fi
.RE
.SS "Configuration"
.SS "\fBforce\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Removes various protections against unfortunate side effects, common mistakes, unnecessary performance degradation, and malicious input.
.RS 0
.IP \(bu 4
Allow clobbering non-npm files in global installs.
.IP \(bu 4
Allow the \fBnpm version\fR command to work on an unclean git repository.
.IP \(bu 4
Allow deleting the cache folder with \fBnpm cache clean\fR.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of npm.
.IP \(bu 4
Allow installing packages that have an \fBengines\fR declaration requiring a different version of \fBnode\fR, even if \fB--engine-strict\fR is enabled.
.IP \(bu 4
Allow \fBnpm audit fix\fR to install modules outside your stated dependency range (including SemVer-major changes).
.IP \(bu 4
Allow unpublishing all versions of a published package.
.IP \(bu 4
Allow conflicting peerDependencies to be installed in the root project.
.IP \(bu 4
Implicitly set \fB--yes\fR during \fBnpm init\fR.
.IP \(bu 4
Allow clobbering existing values in \fBnpm pkg\fR
.IP \(bu 4
Allow unpublishing of entire packages (not just a single version).
.RE 0

.P
If you don't have a clear idea of what you want to do, it is strongly recommended that you do not use this option!
.SS "\fBjson\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Whether or not to output JSON data, rather than the normal output.
.RS 0
.IP \(bu 4
In \fBnpm pkg set\fR it enables parsing set values with JSON.parse() before saving them to your \fBpackage.json\fR.
.RE 0

.P
Not supported by all npm commands.
.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SH "SEE ALSO"
.RS 0
.IP \(bu 4
npm help install
.IP \(bu 4
npm help init
.IP \(bu 4
npm help config
.IP \(bu 4
npm help workspaces
.RE 0
