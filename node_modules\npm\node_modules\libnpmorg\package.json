{"name": "libnpmorg", "version": "8.0.0", "description": "Programmatic api for `npm org` commands", "author": "GitHub Inc.", "main": "lib/index.js", "keywords": ["libnpm", "npm", "package manager", "api", "orgs", "teams"], "license": "ISC", "scripts": {"lint": "npm run eslint", "test": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/template-oss": "4.23.6", "minipass": "^7.1.1", "nock": "^13.3.3", "tap": "^16.3.8"}, "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmorg"}, "bugs": "https://github.com/npm/libnpmorg/issues", "homepage": "https://npmjs.com/package/libnpmorg", "dependencies": {"aproba": "^2.0.0", "npm-registry-fetch": "^18.0.1"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}