# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[r'D:\自编小程序\New_MICRA_augment调整版0728'],
    binaries=[],
    datas=[
    (r'D:\自编小程序\New_MICRA_augment调整版0728\page\index.html', r'page'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\page\violation_feedback.html', r'page'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\css\bootstrap-icons.css', r'static\css'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\css\bootstrap.min.css', r'static\css'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\css\dataTables.bootstrap5.min.css', r'static\css'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\js\bootstrap.bundle.min.js', r'static\js'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\js\dataTables.bootstrap5.min.js', r'static\js'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\js\jquery-3.6.0.min.js', r'static\js'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\static\js\jquery.dataTables.min.js', r'static\js'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\config.ini', r'.'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\output', r'output'),
    (r'D:\自编小程序\New_MICRA_augment调整版0728\uploads', r'uploads'),
],
    hiddenimports=[
    'flask',
    'flask.templating',
    'jinja2',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'json',
    'logging',
    'threading',
    'queue',
    'datetime',
    'uuid',
    'pathlib',
    'sqlite3',
    'encodings.utf_8',
    'encodings.gbk',
    'encodings.cp936',
    'pandas',
    'openpyxl',
    'random',
    'time',
    'os',
    'random_sampler',
    'psycopg2',
    'psycopg2._psycopg',
    'psycopg2.extensions',
    'oracledb',
    'psycopg',
],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
        'fastapi',
        'uvicorn',
        'gunicorn',
        'redis',
        'aioredis',
        'asyncpg',
        'aiomysql',
        'aiohttp',
        'requests',
        'jieba',
        'structlog',
        'prometheus_client',
        'ujson',
        'orjson'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='飞检违规反馈',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='飞检违规反馈_new',
)
