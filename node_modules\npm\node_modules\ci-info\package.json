{"name": "ci-info", "version": "4.2.0", "description": "Get details about the current Continuous Integration environment", "main": "index.js", "typings": "index.d.ts", "type": "commonjs", "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "repository": "github:watson/ci-info", "bugs": "https://github.com/watson/ci-info/issues", "homepage": "https://github.com/watson/ci-info", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sibiraj-s"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "keywords": ["ci", "continuous", "integration", "test", "detect"], "files": ["vendors.json", "index.js", "index.d.ts", "CHANGELOG.md"], "scripts": {"lint:fix": "standard --fix", "test": "standard && node test.js", "prepare": "husky install || true"}, "devDependencies": {"clear-module": "^4.1.2", "husky": "^9.1.7", "publint": "^0.3.8", "standard": "^17.1.2", "tape": "^5.9.0"}, "engines": {"node": ">=8"}}