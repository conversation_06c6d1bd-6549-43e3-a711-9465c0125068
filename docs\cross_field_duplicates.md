# 交叉字段重复检测功能

**开发日期：** 2025-07-21  
**开发人员：** Augment Agent  
**版本：** v1.0  

## 功能背景

根据用户反馈："每个规则重复组中，如果另一个医保名称也有重复名称也标出来。例如医保名称2重复组40有2条规则，医保名称2中吸痰护理、呼吸机吸痰护理是重复项，2条规则的医保项目名称1中如果也有重复的名称也标注出来。"

## 功能概述

在重复规则分组的基础上，增加了交叉字段重复检测功能：
- 在医保名称1重复组中，检测该组规则在医保名称2字段中是否也存在重复项目
- 在医保名称2重复组中，检测该组规则在医保名称1字段中是否也存在重复项目
- 为不同类型的重复项目提供不同的高亮显示效果

## 功能特性

### 1. 双重重复检测

#### 主要重复项目（分组依据）
- **医保名称1重复组**：基于医保名称1字段的重复项目进行分组
- **医保名称2重复组**：基于医保名称2字段的重复项目进行分组

#### 交叉字段重复项目（组内其他重复）
- **医保名称1重复组**：额外检测该组规则在医保名称2字段中的重复项目
- **医保名称2重复组**：额外检测该组规则在医保名称1字段中的重复项目

### 2. 分层高亮显示

#### 主要重复项目高亮
- **医保名称1字段**：蓝色背景 (`bg-primary text-white`)
- **医保名称2字段**：绿色背景 (`bg-success text-white`)

#### 交叉字段重复项目高亮
- **医保名称1字段**：浅蓝色背景 (`bg-info text-white`)
- **医保名称2字段**：深色背景 (`bg-dark text-white`)

### 3. 信息展示优化

#### 分组信息区域
```html
<!-- 分组依据重复项目 -->
<i class="bi bi-tags"></i> 分组依据重复项目:
<span class="badge bg-warning text-dark">医保名称1: 血常规检查</span>

<!-- 组内其他重复项目 -->
<i class="bi bi-plus-circle"></i> 组内其他重复项目:
<span class="badge bg-secondary text-white">医保名称2: CT扫描</span>
```

#### 表格高亮效果
- 鼠标悬停显示项目类型提示
- 不同类型使用不同颜色区分
- 保持视觉层次清晰

## 技术实现

### 1. 后端算法实现

#### 交叉字段重复检测函数
```python
def find_cross_field_duplicates(rules, field_name):
    """
    检查规则组内在指定字段中是否存在重复项目
    
    参数:
    - rules: 规则列表
    - field_name: 要检查的字段名称（"医保名称1" 或 "医保名称2"）
    
    返回:
    - 该字段中的重复项目列表
    """
    try:
        # 收集该字段的所有项目名称
        field_items = []
        field_key = '医保名称1' if field_name == '医保名称1' else '医保名称2'
        
        for rule in rules:
            field_value = rule.get(field_key, '')
            if field_value:
                # 使用相同的分隔符拆分
                items = [item.strip() for item in re.split(r'[、,，|;；]', field_value) if item.strip()]
                field_items.extend(items)
        
        # 统计项目出现次数
        item_counts = {}
        for item in field_items:
            item_counts[item] = item_counts.get(item, 0) + 1
        
        # 找出重复项目（出现次数 > 1）
        duplicate_items = [item for item, count in item_counts.items() if count > 1]
        
        # 格式化为带字段标识的列表
        cross_field_duplicates = [f"{field_name}: {item}" for item in duplicate_items]
        
        return cross_field_duplicates
        
    except Exception as e:
        app.logger.error(f"检查交叉字段重复项目时出错: {e}")
        return []
```

#### 分组结果增强
```python
# 医保名称1重复组
cross_field_duplicates = find_cross_field_duplicates(group_info['rules'], '医保名称2')
result_groups.append({
    'category': '医保名称1重复',
    'common_medical_names': group_info['common_names'],
    'cross_field_duplicates': cross_field_duplicates,  # 新增字段
    # 其他字段...
})

# 医保名称2重复组
cross_field_duplicates = find_cross_field_duplicates(group_info['rules'], '医保名称1')
result_groups.append({
    'category': '医保名称2重复',
    'common_medical_names': group_info['common_names'],
    'cross_field_duplicates': cross_field_duplicates,  # 新增字段
    # 其他字段...
})
```

### 2. 前端显示实现

#### 高亮函数增强
```javascript
// 根据字段类型和项目来源确定高亮样式
const getHighlightClass = (fieldType, itemSource) => {
    // 分组依据的重复项目（主要重复项目）
    if (itemSource === 'primary') {
        switch (fieldType) {
            case '医保名称1':
                return 'bg-primary text-white';  // 蓝色背景
            case '医保名称2':
                return 'bg-success text-white';  // 绿色背景
        }
    }
    // 交叉字段的重复项目（次要重复项目）
    else if (itemSource === 'cross') {
        switch (fieldType) {
            case '医保名称1':
                return 'bg-info text-white';     // 浅蓝色背景
            case '医保名称2':
                return 'bg-dark text-white';     // 深色背景
        }
    }
    return 'bg-warning text-dark';  // 默认样式
};
```

#### 表格高亮调用
```javascript
// 合并主要重复项目和交叉字段重复项目
${highlightCommonNames(rule.医保名称1 || '', 
    [...(group.common_medical_names || []), ...(group.cross_field_duplicates || [])], 
    '医保名称1')}

${highlightCommonNames(rule.医保名称2 || '', 
    [...(group.common_medical_names || []), ...(group.cross_field_duplicates || [])], 
    '医保名称2')}
```

#### 分组信息显示
```javascript
// 分组依据重复项目
<i class="bi bi-tags"></i> 分组依据重复项目:
${group.common_medical_names.map(name =>
    `<span class="badge bg-warning text-dark me-1">${name}</span>`
).join('')}

// 组内其他重复项目
${(group.cross_field_duplicates && group.cross_field_duplicates.length > 0) ? `
    <br><small class="text-muted mt-1">
        <i class="bi bi-plus-circle"></i> 组内其他重复项目:
        ${group.cross_field_duplicates.map(name =>
            `<span class="badge bg-secondary text-white me-1">${name}</span>`
        ).join('')}
    </small>
` : ''}
```

## 用户体验改进

### 1. 全面性提升

#### 改进前
- 只显示分组依据的重复项目
- 可能遗漏组内其他字段的重复情况
- 重复规则审查不够全面

#### 改进后
- 显示分组依据的重复项目
- 额外显示组内其他字段的重复项目
- 提供更全面的重复规则审查

### 2. 视觉层次

#### 颜色编码系统
- **主要重复**：使用鲜明的颜色（蓝色、绿色）
- **次要重复**：使用相对柔和的颜色（浅蓝色、深色）
- **信息标签**：使用中性颜色（黄色、灰色）

#### 信息组织
- **分组信息**：清晰区分两类重复项目
- **表格高亮**：直观显示所有重复项目
- **提示信息**：鼠标悬停显示项目类型

### 3. 决策支持

#### 更准确的判断
- 用户可以看到每个分组中的所有重复情况
- 帮助识别可能被忽略的重复项目
- 提高取消采用决策的准确性

#### 操作指导
- 颜色深浅提示重复项目的重要性
- 分类显示帮助理解重复关系
- 全面信息支持精确操作

## 应用场景

### 场景示例

#### 医保名称2重复组
**分组依据**：医保名称2字段中的"吸痰护理、呼吸机吸痰护理"重复

**规则A**：
- 医保名称1：血常规检查、尿常规检查
- 医保名称2：吸痰护理、CT扫描

**规则B**：
- 医保名称1：血常规检查、心电图检查  
- 医保名称2：呼吸机吸痰护理、MRI检查

#### 检测结果
- **分组依据重复项目**：医保名称2: 吸痰护理、医保名称2: 呼吸机吸痰护理
- **组内其他重复项目**：医保名称1: 血常规检查

#### 高亮效果
- **医保名称1字段**：血常规检查（浅蓝色高亮）
- **医保名称2字段**：吸痰护理、呼吸机吸痰护理（绿色高亮）

## 测试验证

### 1. 功能测试

创建了专门的测试脚本 `test_cross_field_duplicates.py`：
```bash
python test_cross_field_duplicates.py [hospital_id]
```

### 2. 测试内容

#### API功能测试
- 验证交叉字段重复检测算法
- 检查返回数据结构的完整性
- 确认不同分组类别的处理逻辑

#### 前端显示测试
- 验证高亮颜色的正确性
- 检查分组信息的显示效果
- 确认鼠标悬停提示功能

#### 逻辑一致性测试
- 手动验证检测结果的准确性
- 对比算法结果与预期结果
- 确保不同场景下的稳定性

### 3. 边界情况

#### 数据边界
- 空字段处理
- 特殊字符处理
- 大量数据处理

#### 逻辑边界
- 单规则分组
- 无交叉重复情况
- 全部字段重复情况

## 部署说明

### 1. 文件修改

- **`app.py`**：
  - 新增`find_cross_field_duplicates`函数
  - 修改分组结果构建逻辑
  - 添加交叉字段重复检测调用

- **`page/hospital_rules.html`**：
  - 修改高亮函数支持双重高亮
  - 更新分组信息显示模板
  - 添加新的CSS样式定义

### 2. 部署步骤

1. 重启后端服务
2. 清除浏览器缓存
3. 测试交叉字段检测功能
4. 验证前端高亮显示效果

### 3. 验证清单

- [ ] 交叉字段重复检测算法正常工作
- [ ] 分组信息正确显示两类重复项目
- [ ] 表格中的高亮效果符合预期
- [ ] 不同颜色正确区分重复项目类型
- [ ] 鼠标悬停提示信息正常显示
- [ ] 各种边界情况处理正确

## 后续优化

### 1. 功能扩展

- **权重系统**：为不同类型的重复项目设置权重
- **智能建议**：基于重复情况提供取消采用建议
- **批量操作**：支持按重复类型进行批量操作

### 2. 性能优化

- **缓存机制**：缓存交叉字段检测结果
- **并行处理**：并行执行主要和交叉字段检测
- **增量更新**：只检测变化的规则组

### 3. 用户体验

- **自定义颜色**：允许用户自定义高亮颜色
- **详细说明**：提供更详细的重复关系说明
- **导出功能**：支持导出包含交叉重复信息的报告

## 联系信息

如有问题或需要技术支持，请联系：
- **开发人员**：Augment Agent
- **开发日期**：2025-07-21
- **文档版本**：v1.0
