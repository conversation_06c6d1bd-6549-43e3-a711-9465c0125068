# Design Document

## Overview

This document outlines the technical design to fix two critical data persistence issues in the rule knowledge base system:
1. The "所属领域" (Domain) field not being saved properly
2. The "规则内涵" (Rule Content) field being lost during batch AI processing

## Architecture

The fixes will be implemented in the frontend JavaScript code within the `rule_knowledge_base.html` file, focusing on:
- Form data collection logic in the `saveRule()` function
- Batch AI processing data preservation in the `saveBatchResults()` function
- Field mapping and validation improvements

## Components and Interfaces

### 1. Form Data Collection Component

**Location:** `saveRule()` function (lines ~1200-1300)

**Current Issue:** The "所属领域" field is being excluded from the medical data collection logic, causing it to be lost during save operations.

**Fix:** Move "所属领域" from medical data exclusion to main form data collection.

### 2. Batch AI Processing Component

**Location:** `saveBatchResults()` function (lines ~2800-3000)

**Current Issue:** The batch update API call is replacing all rule data instead of merging with existing data, causing "规则内涵" to be lost.

**Fix:** Implement proper data merging logic that preserves existing fields while updating only AI-analyzed fields.

### 3. API Integration Component

**Current Issue:** The batch update API endpoint may not be handling partial updates correctly.

**Fix:** Ensure the frontend sends complete data objects that include both existing and new field values.

## Data Models

### Rule Form Data Structure
```javascript
{
  // Main rule fields (preserved)
  id: string,
  规则名称: string,
  规则内涵: string,  // Must be preserved during batch AI
  所属领域: string,   // Must be included in main form data
  
  // AI-analyzed fields (updated during batch processing)
  医保名称1: string,
  医保名称2: string,
  类型: string,
  违规数量: number,
  // ... other AI fields
}
```

### Batch Processing Data Flow
```
1. Collect existing rule data
2. Apply AI analysis results
3. Merge data (existing + AI results)
4. Send complete merged data to API
5. Preserve all non-AI fields
```

## Error Handling

### Form Save Validation
- Validate that all required fields are collected before sending to API
- Provide specific error messages for missing field data
- Log field collection issues for debugging

### Batch Processing Error Recovery
- If batch save fails, preserve original data
- Provide rollback capability for failed batch operations
- Clear error reporting for data loss issues

## Testing Strategy

### Unit Testing Approach
1. Test form data collection with all field combinations
2. Test batch processing data preservation
3. Test error scenarios and data recovery
4. Validate field mapping consistency

### Integration Testing
1. Test complete save workflows
2. Test batch AI processing end-to-end
3. Verify data persistence across page reloads
4. Test with various rule configurations

### Manual Testing Scenarios
1. Create new rule with "所属领域" selection
2. Edit existing rule and verify "所属领域" preservation
3. Run batch AI processing on rules with existing "规则内涵"
4. Verify no data loss in any workflow