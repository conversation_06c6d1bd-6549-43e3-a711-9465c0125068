.TH "NPM-START" "1" "May 2025" "NPM@11.4.0" ""
.SH "NAME"
\fBnpm-start\fR - Start a package
.SS "Synopsis"
.P
.RS 2
.nf
npm start \[lB]-- <args>\[rB]
.fi
.RE
.SS "Description"
.P
This runs a predefined command specified in the \fB"start"\fR property of a package's \fB"scripts"\fR object.
.P
If the \fB"scripts"\fR object does not define a \fB"start"\fR property, npm will run \fBnode server.js\fR.
.P
Note that this is different from the default node behavior of running the file specified in a package's \fB"main"\fR attribute when evoking with \fBnode .\fR
.P
As of \fB\fBnpm@2.0.0\fR\fR \fI\(lahttps://blog.npmjs.org/post/98131109725/npm-2-0-0\(ra\fR, you can use custom arguments when executing scripts. Refer to npm help run for more details.
.SS "Example"
.P
.RS 2
.nf
{
  "scripts": {
    "start": "node foo.js"
  }
}
.fi
.RE
.P
.RS 2
.nf
npm start

> npm@x.x.x start
> node foo.js

(foo.js output would be here)

.fi
.RE
.SS "Configuration"
.SS "\fBignore-scripts\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If true, npm does not run scripts specified in package.json files.
.P
Note that commands explicitly intended to run a particular script, such as \fBnpm start\fR, \fBnpm stop\fR, \fBnpm restart\fR, \fBnpm test\fR, and \fBnpm run\fR will still run their intended script if \fBignore-scripts\fR is set, but they will \fInot\fR run any pre- or post-scripts.
.SS "\fBscript-shell\fR"
.RS 0
.IP \(bu 4
Default: '/bin/sh' on POSIX systems, 'cmd.exe' on Windows
.IP \(bu 4
Type: null or String
.RE 0

.P
The shell to use for scripts run with the \fBnpm exec\fR, \fBnpm run\fR and \fBnpm
init <package-spec>\fR commands.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help run
.IP \(bu 4
npm help scripts
.IP \(bu 4
npm help test
.IP \(bu 4
npm help restart
.IP \(bu 4
npm help stop
.RE 0
