{"name": "binary-extensions", "version": "3.1.0", "description": "List of binary file extensions", "license": "MIT", "repository": "sindresorhus/binary-extensions", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18.20"}, "scripts": {"//test": "xo && ava && tsd", "test": "ava && tsd"}, "files": ["index.js", "index.d.ts", "binary-extensions.json"], "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "devDependencies": {"ava": "^6.1.2", "tsd": "^0.31.0", "xo": "^0.58.0"}}