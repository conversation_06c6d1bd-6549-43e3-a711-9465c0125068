.TH "NPM-EDIT" "1" "May 2025" "NPM@11.4.0" ""
.SH "NAME"
\fBnpm-edit\fR - Edit an installed package
.SS "Synopsis"
.P
.RS 2
.nf
npm edit <pkg>\[lB]/<subpkg>...\[rB]
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
Selects a dependency in the current project and opens the package folder in the default editor (or whatever you've configured as the npm \fBeditor\fR config -- see \fB\fBnpm-config\fR\fR \fI\(lanpm-config\(ra\fR.)
.P
After it has been edited, the package is rebuilt so as to pick up any changes in compiled packages.
.P
For instance, you can do \fBnpm install connect\fR to install connect into your package, and then \fBnpm edit connect\fR to make a few changes to your locally installed copy.
.SS "Configuration"
.SS "\fBeditor\fR"
.RS 0
.IP \(bu 4
Default: The EDITOR or VISUAL environment variables, or '%SYSTEMROOT%\[rs]notepad.exe' on Windows, or 'vi' on Unix systems
.IP \(bu 4
Type: String
.RE 0

.P
The command to run for \fBnpm edit\fR and \fBnpm config edit\fR.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help folders
.IP \(bu 4
npm help explore
.IP \(bu 4
npm help install
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.RE 0
