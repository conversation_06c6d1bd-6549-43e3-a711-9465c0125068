# 重复规则审查功能说明文档

**功能版本：** v1.0  
**开发日期：** 2025-07-20  
**开发人员：** Augment Agent  

## 功能概述

重复规则审查功能是医院规则推荐系统的新增功能，用于识别和管理医院已采用规则中的重复规则。该功能通过分析医保项目名称的交集来检测重复规则，并提供批量取消采用的管理工具。

## 功能特性

### 1. 智能重复检测
- **检测依据**：基于`医保名称1`和`医保名称2`字段进行交集分析
- **匹配算法**：支持完全匹配、部分匹配和交集匹配
- **分组算法**：使用并查集算法将有关联的重复规则分组
- **相似度计算**：根据共同医保项目数量计算相似度

### 2. 可视化分组展示
- **分组显示**：将重复规则按相似度分组展示
- **高亮显示**：共同医保项目名称高亮标记
- **折叠展开**：支持分组的折叠和展开操作
- **统计信息**：显示总规则数、重复规则数、分组数等统计信息

### 3. 批量管理功能
- **批量选择**：支持单个规则选择和整组选择
- **批量取消采用**：一键取消采用多条重复规则
- **操作确认**：提供操作前确认提示
- **状态反馈**：实时显示操作进度和结果

### 4. 用户体验优化
- **响应式设计**：适配不同屏幕尺寸
- **加载状态**：显示数据加载和处理状态
- **错误处理**：友好的错误提示和异常处理
- **操作引导**：清晰的操作按钮和提示信息

## 技术实现

### 后端API接口

#### 1. 重复规则分析接口
```
GET /api/hospital-rules/duplicate-analysis/<hospital_id>
```

**功能**：分析指定医院的重复规则  
**参数**：
- `hospital_id`：医院ID

**返回数据**：
```json
{
    "success": true,
    "duplicate_groups": [
        {
            "group_id": 1,
            "rules": [...],
            "common_medical_names": ["项目A", "项目B"],
            "similarity": 0.85,
            "rule_count": 3
        }
    ],
    "total_rules": 50,
    "duplicate_rules": 8,
    "duplicate_groups_count": 2
}
```

#### 2. 批量取消采用接口
```
POST /api/hospital-rules/batch-unadopt
```

**功能**：批量取消采用指定规则  
**请求数据**：
```json
{
    "rule_ids": [1, 2, 3],
    "reason": "重复规则审查-批量取消采用"
}
```

**返回数据**：
```json
{
    "success": true,
    "updated_count": 3,
    "failed_count": 0,
    "failed_rules": [],
    "message": "成功取消采用 3 条规则"
}
```

### 前端界面组件

#### 1. 重复规则审查按钮
- **位置**：批量操作工具栏
- **显示条件**：仅在已采用规则页面显示
- **样式**：Bootstrap警告色按钮

#### 2. 重复规则分析模态框
- **尺寸**：超大模态框（modal-xl）
- **内容**：重复规则分组展示
- **操作**：批量选择和取消采用

#### 3. 分组展示组件
- **卡片布局**：每个重复组使用Bootstrap卡片
- **折叠面板**：支持展开和折叠
- **表格显示**：规则详情使用响应式表格

### 数据库查询逻辑

#### 主查询SQL
```sql
SELECT h.适用ID, h.规则ID, r.规则名称, h.匹配项目, h.创建时间,
       c.医保名称1, c.医保名称2, c.城市, c.规则来源, c.规则内涵
FROM 医院适用规则表 h
JOIN 飞检规则知识库 r ON h.规则ID = r.ID
LEFT JOIN 规则医保编码对照 c ON h.对照ID = c.对照ID
WHERE h.医院ID = :hospital_id AND h.状态 = '已采用'
ORDER BY h.创建时间 DESC
```

#### 重复检测算法
1. **医保项目名称解析**：使用正则表达式分割医保名称字段
2. **索引构建**：建立医保项目名称到规则的映射关系
3. **重复识别**：找出被多个规则引用的医保项目名称
4. **分组算法**：使用深度优先搜索进行规则分组
5. **相似度计算**：基于共同医保项目数量计算相似度

## 使用说明

### 操作步骤

1. **进入功能页面**
   - 打开医院规则管理页面
   - 选择要分析的医院
   - 点击"已采用"按钮查看已采用规则

2. **启动重复分析**
   - 在批量操作工具栏中点击"重复规则审查"按钮
   - 系统自动分析重复规则并显示结果

3. **查看分析结果**
   - 查看统计信息（总规则数、重复规则数、分组数）
   - 浏览重复规则分组
   - 查看共同医保项目名称（高亮显示）

4. **管理重复规则**
   - 选择要取消采用的重复规则
   - 可以单个选择或整组选择
   - 点击"批量取消采用"执行操作

5. **确认操作结果**
   - 查看操作结果提示
   - 重新分析验证重复规则是否已处理

### 界面说明

#### 统计信息区域
- **总规则数**：医院已采用规则总数
- **重复规则数**：检测到的重复规则数量
- **重复组数**：重复规则分组数量

#### 重复组展示
- **组标题**：显示组编号、规则数量、相似度
- **共同项目**：显示该组规则的共同医保项目名称
- **规则列表**：表格形式显示组内规则详情

#### 操作按钮
- **展开/折叠**：控制所有分组的展开状态
- **全选/清除**：批量选择操作
- **批量取消采用**：执行批量操作

## 配置说明

### 检测参数配置
- **分隔符**：医保项目名称分隔符（默认：`、,，|;；`）
- **最小相似度**：分组最小相似度阈值（默认：0.1）
- **最大分组数**：单次分析最大分组数（默认：无限制）

### 界面配置
- **每页显示**：重复组每页显示数量（默认：全部显示）
- **高亮颜色**：共同医保项目高亮颜色（默认：黄色）
- **按钮样式**：操作按钮的Bootstrap样式类

## 注意事项

### 使用限制
1. **权限要求**：需要医院规则管理权限
2. **数据要求**：医院必须有已采用规则
3. **性能考虑**：大量规则时分析可能较慢

### 操作建议
1. **定期检查**：建议定期执行重复规则审查
2. **谨慎操作**：取消采用前请仔细确认
3. **备份数据**：重要操作前建议备份数据

### 故障排除
1. **无重复规则**：显示"未发现重复规则"提示
2. **加载失败**：检查网络连接和服务状态
3. **操作失败**：查看错误提示并重试

## 更新日志

### v1.0 (2025-07-20)
- ✅ 实现重复规则检测算法
- ✅ 添加重复规则分组显示
- ✅ 实现批量取消采用功能
- ✅ 优化用户界面和交互体验
- ✅ 添加完整的错误处理和状态反馈

## 技术支持

如有问题或建议，请联系：
- **开发人员**：Augment Agent
- **开发日期**：2025-07-20
- **文档版本**：v1.0
