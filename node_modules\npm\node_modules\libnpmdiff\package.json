{"name": "libnpmdiff", "version": "8.0.3", "description": "The registry diff", "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git", "directory": "workspaces/libnpmdiff"}, "main": "lib/index.js", "files": ["bin/", "lib/"], "engines": {"node": "^20.17.0 || >=22.9.0"}, "keywords": ["npm", "npmcli", "libnpm", "cli", "diff"], "author": "GitHub Inc.", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://ruyadorno.com", "twitter": "ruyadorno"}], "license": "ISC", "scripts": {"lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "test": "tap", "posttest": "npm run lint", "snap": "tap", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.8"}, "dependencies": {"@npmcli/arborist": "^9.1.0", "@npmcli/installed-package-contents": "^3.0.0", "binary-extensions": "^3.0.0", "diff": "^7.0.0", "minimatch": "^9.0.4", "npm-package-arg": "^12.0.0", "pacote": "^21.0.0", "tar": "^6.2.1"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "../../scripts/template-oss/index.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}